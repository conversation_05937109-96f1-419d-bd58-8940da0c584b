import { Config, Inject, InjectClient, Provide } from '@midwayjs/core';
import { SignParams, WePay } from '../common/wePay';
import { Customer, Order, OrderDetail, ServiceChangeLog } from '../entity';
import {
  AxiosResponse,
  HttpService,
  HttpServiceFactory,
} from '@midwayjs/axios';
import { Context } from '@midwayjs/koa';
import { CustomError } from '../error/custom.error';
import { OrderStatus, OrderStatusChangeType } from '../common/Constant';
import {
  MembershipCardOrder,
  MembershipCardOrderStatus,
} from '../entity/membership-card-order.entity';
import { CouponOrder, CouponOrderStatus } from '../entity/coupon-order.entity';
import {
  AdditionalServiceOrder,
  AdditionalServiceOrderStatus,
} from '../entity/additional-service-order.entity';
import { MessageBroadcastService } from './message-broadcast.service';
import { WeappService } from './weapp.service';
import { AdditionalServiceOrderService } from './additional-service-order.service';

@Provide()
export class WepayService {
  @Inject()
  ctx: Context;

  @Inject()
  wePay: WePay;

  @Config('weapp')
  weappConfig: {
    appid: string;
    secret: string;
    sym_sn: string;
    sym_key: string;
  };

  @Config('wePay')
  wePayConfig: {
    sp_mchid: string;
    sp_appid: string;
    pem_sn: string;
  };

  @InjectClient(HttpServiceFactory, 'weapp_pay')
  weappPayService: HttpService;

  @Inject()
  messageBroadcastService: MessageBroadcastService;

  @Inject()
  weappService: WeappService;

  @Inject()
  additionalServiceOrderService: AdditionalServiceOrderService;

  private analisisRes(res: AxiosResponse) {
    if (res.status === 200) {
      return res.data;
    }
    return {};
  }

  /** JSAPI/小程序下单 */
  async jsapi(appid: string, sn: string) {
    // 判断是服务订单、权益卡订单、代金券订单还是追加服务订单
    if (sn.startsWith('MC')) {
      return await this.jsapiForMembershipCard(appid, sn);
    } else if (sn.startsWith('CP')) {
      return await this.jsapiForCoupon(appid, sn);
    } else if (sn.startsWith('ADD')) {
      return await this.jsapiForAdditionalService(appid, sn);
    } else {
      return await this.jsapiForService(appid, sn);
    }
  }

  /** 服务订单JSAPI/小程序下单 */
  async jsapiForService(appid: string, sn: string) {
    const order = await Order.findOne({
      where: {
        sn,
      },
      include: [
        {
          model: OrderDetail,
        },
        {
          model: Customer,
        },
      ],
    });
    if (!order) {
      throw new CustomError('订单不存在');
    }
    if (order.status !== OrderStatus.待付款) {
      throw new CustomError('订单状态不正确');
    }
    if (order.prepay_id) {
      return { prepay_id: order.prepay_id };
    }
    // console.log('order', order);
    // 支付结束时间，15分钟过期
    const date = new Date(Date.now() + 1000 * 60 * 15);
    const time_expire = date.toISOString().replace('Z', '+08:00');
    console.log('time_expire: ', time_expire);
    const body = {
      appid,
      mchid: this.wePayConfig.sp_mchid,
      description: '贝宠服务订单支付',
      out_trade_no: order.sn,
      time_expire,
      // attach: String(order.id),
      notify_url: 'https://manager.petsjoylife.com/api/openapi/pay/callback',
      amount: {
        total: Math.round(order.totalFee * 100),
        currency: 'CNY',
      },
      payer: {
        openid: order.customer.openid,
      },
    };
    console.log('body: ', body);
    const requestInfo = {
      // url: '/v3/pay/partner/transactions/jsapi',
      url: '/v3/pay/transactions/jsapi',
      body,
    };
    this.ctx.logger.info('【发起付款】：', requestInfo);
    try {
      if (requestInfo.body.amount.total > 0) {
        const signature = this.wePay.genSign({
          method: 'POST',
          url: requestInfo.url,
          body: requestInfo.body,
        });
        const res = await this.weappPayService.post(requestInfo.url, body, {
          headers: {
            Authorization: signature,
            Accept: 'application/json',
            'Content-Type': 'application/json',
          },
        });
        this.ctx.logger.info('【发起付款结果】：', {
          status: res.status,
          data: res.data,
        });
        const data = this.analisisRes(res);
        // console.log('data: ', data);
        if (data.prepay_id) {
          await order.update({
            prepay_id: data.prepay_id,
          });
        }
        return data;
      }
      // 0元订单直接返回成功，这里只是支付前的动作，所以不需要更新订单状态，也不写日志
      return '0';
    } catch (error) {
      if (error.response?.data?.message === '该订单已支付') {
        console.log(error.response.data.message);
        this.ctx.logger.info('【订单已支付】：', order);
        await order.update({
          status: OrderStatus.待接单,
        });
        await ServiceChangeLog.create({
          orderId: order.id,
          changeType: OrderStatusChangeType.付款,
          description: '订单已支付，由系统自动更新订单状态',
        });

        // 广播新订单消息
        await this.messageBroadcastService.broadcastNewOrder(order);

        return '订单已支付，由系统自动更新订单状态';
      }
      this.ctx.logger.error('【发起付款错误】：', error.response);
      throw error;
    }
  }

  /** 权益卡订单JSAPI/小程序下单 */
  async jsapiForMembershipCard(appid: string, sn: string) {
    const order = await MembershipCardOrder.findOne({
      where: {
        sn,
      },
      include: [
        {
          model: Customer,
        },
      ],
    });
    if (!order) {
      throw new CustomError('订单不存在');
    }
    if (order.status !== MembershipCardOrderStatus.PENDING_PAYMENT) {
      throw new CustomError('订单状态不正确');
    }
    if (order.prepayId) {
      return { prepay_id: order.prepayId };
    }
    // 支付结束时间，15分钟过期
    const date = new Date(Date.now() + 1000 * 60 * 15);
    const time_expire = date.toISOString().replace('Z', '+08:00');
    console.log('time_expire: ', time_expire);
    const body = {
      appid,
      mchid: this.wePayConfig.sp_mchid,
      description: '贝宠权益卡购买',
      out_trade_no: order.sn,
      time_expire,
      notify_url: 'https://manager.petsjoylife.com/api/openapi/pay/callback',
      amount: {
        total: Math.round(order.amount * 100), // 转换为分
        currency: 'CNY',
      },
      payer: {
        openid: order.customer.openid,
      },
    };
    console.log('body: ', body);
    const requestInfo = {
      url: '/v3/pay/transactions/jsapi',
      body,
    };
    this.ctx.logger.info('【发起权益卡付款】：', requestInfo);
    const signature = this.wePay.genSign({
      method: 'POST',
      url: requestInfo.url,
      body: requestInfo.body,
    });
    try {
      const res = await this.weappPayService.post(requestInfo.url, body, {
        headers: {
          Authorization: signature,
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
      });
      this.ctx.logger.info('【发起权益卡付款结果】：', {
        status: res.status,
        data: res.data,
      });
      const data = this.analisisRes(res);
      if (data.prepay_id) {
        await order.update({
          prepayId: data.prepay_id,
        });
      }
      return data;
    } catch (error) {
      if (error.response?.data?.message === '该订单已支付') {
        console.log(error.response.data.message);
        this.ctx.logger.info('【权益卡订单已支付】：', order);
        await order.update({
          status: MembershipCardOrderStatus.PAID,
          payTime: new Date(),
        });
        return '订单已支付，由系统自动更新订单状态';
      }
      this.ctx.logger.error('【发起权益卡付款错误】：', error.response);
      throw error;
    }
  }

  /** 代金券订单JSAPI/小程序下单 */
  async jsapiForCoupon(appid: string, sn: string) {
    const order = await CouponOrder.findOne({
      where: {
        sn,
      },
      include: [
        {
          model: Customer,
        },
      ],
    });
    if (!order) {
      throw new CustomError('订单不存在');
    }
    if (order.status !== CouponOrderStatus.PENDING_PAYMENT) {
      throw new CustomError('订单状态不正确');
    }
    if (order.prepayId) {
      return { prepay_id: order.prepayId };
    }
    // 支付结束时间，15分钟过期
    const date = new Date(Date.now() + 1000 * 60 * 15);
    const time_expire = date.toISOString().replace('Z', '+08:00');
    console.log('time_expire: ', time_expire);
    const body = {
      appid,
      mchid: this.wePayConfig.sp_mchid,
      description: '贝宠代金券购买',
      out_trade_no: order.sn,
      time_expire,
      notify_url: 'https://manager.petsjoylife.com/api/openapi/pay/callback',
      amount: {
        total: Math.round(order.amount * 100), // 转换为分
        currency: 'CNY',
      },
      payer: {
        openid: order.customer.openid,
      },
    };
    console.log('body: ', body);
    const requestInfo = {
      url: '/v3/pay/transactions/jsapi',
      body,
    };
    this.ctx.logger.info('【发起代金券付款】：', requestInfo);
    const signature = this.wePay.genSign({
      method: 'POST',
      url: requestInfo.url,
      body: requestInfo.body,
    });
    try {
      const res = await this.weappPayService.post(requestInfo.url, body, {
        headers: {
          Authorization: signature,
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
      });
      this.ctx.logger.info('【发起代金券付款结果】：', {
        status: res.status,
        data: res.data,
      });
      const data = this.analisisRes(res);
      if (data.prepay_id) {
        await order.update({
          prepayId: data.prepay_id,
        });
      }
      return data;
    } catch (error) {
      if (error.response?.data?.message === '该订单已支付') {
        console.log(error.response.data.message);
        this.ctx.logger.info('【代金券订单已支付】：', order);
        await order.update({
          status: CouponOrderStatus.PAID,
          payTime: new Date(),
        });
        return '订单已支付，由系统自动更新订单状态';
      }
      this.ctx.logger.error('【发起代金券付款错误】：', error.response);
      throw error;
    }
  }

  /** 追加服务订单JSAPI/小程序下单 */
  async jsapiForAdditionalService(appid: string, sn: string) {
    const order = await AdditionalServiceOrder.findOne({
      where: {
        sn,
      },
      include: [
        {
          model: Customer,
        },
      ],
    });
    if (!order) {
      throw new CustomError('追加服务订单不存在');
    }
    if (order.status !== AdditionalServiceOrderStatus.CONFIRMED) {
      throw new CustomError('追加服务订单状态不正确');
    }
    if (order.prepayId) {
      return { prepay_id: order.prepayId };
    }

    // 支付结束时间，15分钟过期
    const date = new Date(Date.now() + 1000 * 60 * 15);
    const time_expire = date.toISOString().replace('Z', '+08:00');
    console.log('time_expire: ', time_expire);
    const body = {
      appid,
      mchid: this.wePayConfig.sp_mchid,
      description: '贝宠追加服务支付',
      out_trade_no: order.sn,
      time_expire,
      notify_url: 'https://manager.petsjoylife.com/api/openapi/pay/callback',
      amount: {
        total: Math.round(order.totalFee * 100),
        currency: 'CNY',
      },
      payer: {
        openid: order.customer.openid,
      },
    };
    console.log('body: ', body);
    const requestInfo = {
      url: '/v3/pay/transactions/jsapi',
      body,
    };
    this.ctx.logger.info('【发起追加服务付款】：', requestInfo);
    try {
      if (requestInfo.body.amount.total > 0) {
        const signature = this.wePay.genSign({
          method: 'POST',
          url: requestInfo.url,
          body: requestInfo.body,
        });
        const res = await this.weappPayService.post(requestInfo.url, body, {
          headers: {
            Authorization: signature,
            Accept: 'application/json',
            'Content-Type': 'application/json',
          },
        });
        this.ctx.logger.info('【发起追加服务付款结果】：', {
          status: res.status,
          data: res.data,
        });
        const data = this.analisisRes(res);
        if (data.prepay_id) {
          await order.update({
            prepayId: data.prepay_id,
          });
        }
        return data;
      }
      // 0元订单直接返回成功，这里只是支付前的动作，所以不需要更新订单状态，也不写日志
      return '0';
    } catch (error) {
      if (error.response?.data?.message === '该订单已支付') {
        console.log(error.response.data.message);
        this.ctx.logger.info('【追加服务订单已支付】：', order);
        await order.update({
          status: AdditionalServiceOrderStatus.PAID,
          payTime: new Date(),
        });
        return '订单已支付，由系统自动更新订单状态';
      }
      this.ctx.logger.error('【发起追加服务付款错误】：', error.response);
      throw error;
    }
  }

  /** 微信支付订单号查询订单 */
  async getTransactionsBySN(sn: string) {
    const requestInfo = {
      url: `/v3/pay/transactions/out-trade-no/${sn}`,
      query: { mchid: this.wePayConfig.sp_mchid },
    };
    console.log('requestInfo: ', requestInfo);
    const signature = this.wePay.genSign({
      method: 'GET',
      url: requestInfo.url,
      query: requestInfo.query,
    });
    try {
      const res = await this.weappPayService.get(requestInfo.url, {
        params: requestInfo.query,
        headers: {
          Authorization: signature,
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
      });
      const data = this.analisisRes(res);
      return data;
    } catch (error) {
      this.ctx.logger.error('【微信支付订单号查询订单】错误：', error.response);
      throw error;
    }
  }

  /**
   * 退款
   *
   * @param {string} sn 订单号
   * @param {number} [refund] 退款金额，可选，单位为元
   */
  async refund(sn: string, refund?: number) {
    const order = await Order.findOne({
      where: {
        sn,
      },
      attributes: ['id', 'sn', 'totalFee', 'status'],
    });
    if (!order) {
      throw new CustomError('订单不存在');
    }
    if (
      !([OrderStatus.待接单, OrderStatus.退款中] as string[]).includes(
        order.status
      )
    ) {
      throw new CustomError('自动退款仅支持已付款且未接单订单的退款');
    }
    const options: SignParams = {
      url: '/v3/refund/domestic/refunds',
      method: 'POST',
      body: {
        out_trade_no: sn,
        out_refund_no: sn,
        reason: '用户申请退款',
        amount: {
          refund: (refund || order.totalFee) * 100,
          total: order.totalFee * 100,
          currency: 'CNY',
        },
      },
    };
    this.ctx.logger.info('【微信支付退款】：', options.body);
    try {
      if (options.body.amount.refund > 0) {
        const signature = this.wePay.genSign({
          method: options.method,
          url: options.url,
          body: options.body,
        });
        const res = await this.weappPayService.post(options.url, options.body, {
          headers: {
            Authorization: signature,
            Accept: 'application/json',
            'Content-Type': 'application/json',
          },
        });
        const data = this.analisisRes(res);
        if (data.refund_id) {
          await order.update({
            status: OrderStatus.已退款,
          });

          // 清除该订单的微信订阅信息
          this.weappService.clearOrderSubscriptions(order.id.toString());

          // 广播取消订单消息
          await this.messageBroadcastService.broadcastCancelOrder(order.id);
          await ServiceChangeLog.create({
            orderId: order.id,
            changeType: OrderStatusChangeType.退款,
            description: '订单已退款，由系统自动更新订单状态',
          });
        }
        return data;
      }
      // 0元订单直接返回成功
      await order.update({
        status: OrderStatus.已退款,
      });

      // 清除该订单的微信订阅信息
      this.weappService.clearOrderSubscriptions(order.id.toString());

      // 广播取消订单消息
      await this.messageBroadcastService.broadcastCancelOrder(order.id);
      await ServiceChangeLog.create({
        orderId: order.id,
        changeType: OrderStatusChangeType.退款,
        description: '订单已退款，由系统自动更新订单状态',
      });
      return '0';
    } catch (error) {
      this.ctx.logger.error('【微信支付退款】错误：', error);
      throw error;
    }
  }

  /**
   * 追加服务订单退款
   *
   * @param {string} sn 追加服务订单号
   * @param {number} [refund] 退款金额，可选，单位为元
   */
  async refundAdditionalService(sn: string, refund?: number) {
    const order = await AdditionalServiceOrder.findOne({
      where: {
        sn,
      },
      attributes: ['id', 'sn', 'totalFee', 'status'],
    });
    if (!order) {
      throw new CustomError('追加服务订单不存在');
    }
    if (
      ![
        AdditionalServiceOrderStatus.PAID,
        AdditionalServiceOrderStatus.COMPLETED,
      ].includes(order.status)
    ) {
      throw new CustomError('只能退款已支付的追加服务订单');
    }

    const refundAmount = refund || Number(order.totalFee);
    const totalAmount = Number(order.totalFee);

    const options: SignParams = {
      url: '/v3/refund/domestic/refunds',
      method: 'POST',
      body: {
        out_trade_no: sn,
        out_refund_no: sn,
        reason: '追加服务退款',
        amount: {
          refund: refundAmount * 100,
          total: totalAmount * 100,
          currency: 'CNY',
        },
      },
    };

    this.ctx.logger.info('【微信支付追加服务退款】：', options.body);

    try {
      if (options.body.amount.refund > 0) {
        const signature = this.wePay.genSign({
          method: options.method,
          url: options.url,
          body: options.body,
        });
        const res = await this.weappPayService.post(options.url, options.body, {
          headers: {
            Authorization: signature,
            Accept: 'application/json',
            'Content-Type': 'application/json',
          },
        });
        const data = this.analisisRes(res);
        if (data.refund_id) {
          await order.update({
            status: AdditionalServiceOrderStatus.REFUNDED,
          });
          this.ctx.logger.info(
            `追加服务订单 ${sn} 微信退款成功，退款ID: ${data.refund_id}`
          );
        }
        return data;
      } else {
        // 0元订单直接更新状态
        await order.update({
          status: AdditionalServiceOrderStatus.REFUNDED,
        });
        this.ctx.logger.info(
          `追加服务订单 ${sn} 为0元订单，直接更新状态为已退款`
        );
        return { refund_id: 'zero_amount_refund' };
      }
    } catch (error) {
      this.ctx.logger.error('追加服务微信退款失败:', error);
      throw new CustomError(`微信退款失败: ${error.message}`);
    }
  }

  /**
   * 查询微信支付状态并同步本地订单状态
   * 这个方法会在查询微信支付状态的同时，根据支付结果更新本地订单状态
   */
  async getTransactionsBySNWithStatusSync(sn: string) {
    this.ctx.logger.info('【查询微信支付状态并同步】：', { sn });

    try {
      // 先查询微信支付状态
      const wechatPaymentResult = await this.getTransactionsBySN(sn);

      this.ctx.logger.info('【微信支付查询结果】：', {
        sn,
        trade_state: wechatPaymentResult?.trade_state,
        transaction_id: wechatPaymentResult?.transaction_id,
      });

      // 如果微信支付成功，尝试同步本地订单状态
      if (
        wechatPaymentResult &&
        wechatPaymentResult.trade_state === 'SUCCESS'
      ) {
        await this.syncLocalOrderStatus(sn, wechatPaymentResult);
      }

      return wechatPaymentResult;
    } catch (error) {
      this.ctx.logger.error('【查询微信支付状态并同步】错误：', {
        sn,
        error: error.message,
      });
      throw error;
    }
  }

  /**
   * 根据微信支付结果同步本地订单状态
   */
  private async syncLocalOrderStatus(sn: string, wechatPaymentResult: any) {
    try {
      if (sn.startsWith('ADD')) {
        // 追加服务订单
        await this.syncAdditionalServiceOrderStatus(sn, wechatPaymentResult);
      } else if (sn.startsWith('MC')) {
        // 权益卡订单
        await this.syncMembershipCardOrderStatus(sn, wechatPaymentResult);
      } else if (sn.startsWith('CP')) {
        // 代金券订单
        await this.syncCouponOrderStatus(sn, wechatPaymentResult);
      } else {
        // 普通服务订单
        await this.syncServiceOrderStatus(sn, wechatPaymentResult);
      }
    } catch (error) {
      this.ctx.logger.error('【同步本地订单状态】错误：', {
        sn,
        error: error.message,
      });
      // 同步失败不应该影响查询结果的返回
    }
  }

  /**
   * 同步追加服务订单状态
   */
  private async syncAdditionalServiceOrderStatus(
    sn: string,
    wechatPaymentResult: any
  ) {
    const order = await AdditionalServiceOrder.findOne({
      where: { sn },
      attributes: ['id', 'sn', 'status', 'orderDetailId', 'payTime'],
    });

    if (!order) {
      this.ctx.logger.warn('【同步追加服务订单状态】本地订单不存在：', sn);
      return;
    }

    // 如果本地订单已经是已支付状态，不需要更新
    if (order.status === AdditionalServiceOrderStatus.PAID) {
      this.ctx.logger.info(
        '【同步追加服务订单状态】订单已经是已支付状态：',
        sn
      );
      return;
    }

    // 如果本地订单不是已确认状态，不能更新为已支付
    if (order.status !== AdditionalServiceOrderStatus.CONFIRMED) {
      this.ctx.logger.warn(
        '【同步追加服务订单状态】订单状态不是已确认，无法更新：',
        {
          sn,
          currentStatus: order.status,
        }
      );
      return;
    }

    // 更新订单状态为已支付
    await order.update({
      status: AdditionalServiceOrderStatus.PAID,
      payTime: new Date(),
    });

    // 更新主订单的追加服务信息
    await this.additionalServiceOrderService.updateMainOrderAdditionalServiceInfo(
      order.orderDetailId
    );

    this.ctx.logger.info('【同步追加服务订单状态】成功更新为已支付：', {
      sn,
      orderId: order.id,
      wechatTransactionId: wechatPaymentResult.transaction_id,
    });
  }

  /**
   * 同步权益卡订单状态
   */
  private async syncMembershipCardOrderStatus(
    sn: string,
    wechatPaymentResult: any
  ) {
    const order = await MembershipCardOrder.findOne({
      where: { sn },
      attributes: ['id', 'sn', 'status', 'payTime'],
    });

    if (!order) {
      this.ctx.logger.warn('【同步权益卡订单状态】本地订单不存在：', sn);
      return;
    }

    if (order.status === MembershipCardOrderStatus.PAID) {
      this.ctx.logger.info('【同步权益卡订单状态】订单已经是已支付状态：', sn);
      return;
    }

    if (order.status !== MembershipCardOrderStatus.PENDING_PAYMENT) {
      this.ctx.logger.warn(
        '【同步权益卡订单状态】订单状态不是待支付，无法更新：',
        {
          sn,
          currentStatus: order.status,
        }
      );
      return;
    }

    await order.update({
      status: MembershipCardOrderStatus.PAID,
      payTime: new Date(),
    });

    this.ctx.logger.info('【同步权益卡订单状态】成功更新为已支付：', {
      sn,
      orderId: order.id,
      wechatTransactionId: wechatPaymentResult.transaction_id,
    });
  }

  /**
   * 同步代金券订单状态
   */
  private async syncCouponOrderStatus(sn: string, wechatPaymentResult: any) {
    const order = await CouponOrder.findOne({
      where: { sn },
      attributes: ['id', 'sn', 'status', 'payTime'],
    });

    if (!order) {
      this.ctx.logger.warn('【同步代金券订单状态】本地订单不存在：', sn);
      return;
    }

    if (order.status === CouponOrderStatus.PAID) {
      this.ctx.logger.info('【同步代金券订单状态】订单已经是已支付状态：', sn);
      return;
    }

    if (order.status !== CouponOrderStatus.PENDING_PAYMENT) {
      this.ctx.logger.warn(
        '【同步代金券订单状态】订单状态不是待支付，无法更新：',
        {
          sn,
          currentStatus: order.status,
        }
      );
      return;
    }

    await order.update({
      status: CouponOrderStatus.PAID,
      payTime: new Date(),
    });

    this.ctx.logger.info('【同步代金券订单状态】成功更新为已支付：', {
      sn,
      orderId: order.id,
      wechatTransactionId: wechatPaymentResult.transaction_id,
    });
  }

  /**
   * 同步服务订单状态
   */
  private async syncServiceOrderStatus(sn: string, wechatPaymentResult: any) {
    const order = await Order.findOne({
      where: { sn },
      attributes: ['id', 'sn', 'status'],
    });

    if (!order) {
      this.ctx.logger.warn('【同步服务订单状态】本地订单不存在：', sn);
      return;
    }

    if (
      order.status === OrderStatus.待接单 ||
      order.status === OrderStatus.服务中 ||
      order.status === OrderStatus.已完成
    ) {
      this.ctx.logger.info('【同步服务订单状态】订单已经是已支付状态：', sn);
      return;
    }

    if (order.status !== OrderStatus.待付款) {
      this.ctx.logger.warn(
        '【同步服务订单状态】订单状态不是待付款，无法更新：',
        {
          sn,
          currentStatus: order.status,
        }
      );
      return;
    }

    await order.update({
      status: OrderStatus.待接单,
    });

    await ServiceChangeLog.create({
      orderId: order.id,
      changeType: OrderStatusChangeType.付款,
      description: '根据微信支付状态同步更新订单状态',
    });

    // 广播新订单消息
    await this.messageBroadcastService.broadcastNewOrder(order);

    this.ctx.logger.info('【同步服务订单状态】成功更新为待接单：', {
      sn,
      orderId: order.id,
      wechatTransactionId: wechatPaymentResult.transaction_id,
    });
  }
}
