import {
  Table,
  Column,
  Model,
  DataType,
  BelongsTo,
  ForeignKey,
  BelongsToMany,
  HasMany,
} from 'sequelize-typescript';
import { Order } from './order.entity';
import { Service } from './service.entity';
import { Pet } from './pet.entity';
import { AdditionalService } from './additional-service.entity';
import { OrderDetailAdditional } from './order-detail-additional.entity';
import { AdditionalServiceOrder } from './additional-service-order.entity';
import { ServiceDurationRecord } from './service-duration-record.entity';

export interface OrderDetailAttributes {
  /** 明细ID */
  id: number;
  /** 关联订单ID */
  orderId: number;
  /** 关联服务ID */
  serviceId: number;
  /** 服务名称，确保删除服务后订单明细的服务名称不丢失 */
  serviceName: string;
  /** 服务基础价格，确保服务价格变更后订单明细的价格不受影响 */
  servicePrice: number;
  /** 关联宠物ID */
  petId?: number;
  /** 宠物名称，确保删除宠物后订单明细的宠物名称不丢失 */
  petName: string;
  /** 宠物类型，确保删除宠物后订单明细的宠物类型不丢失 */
  petType: string;
  /** 宠物品种，确保删除宠物后订单明细的宠物品种不丢失 */
  petBreed?: string;
  /** 下单时间 */
  orderTime: Date;
  /** 状态 */
  status: string;
  /** 用户备注 */
  userRemark?: string;
  /** 关联订单信息 */
  order?: Order;
  /** 关联服务信息 */
  service?: Service;
  /** 关联宠物信息 */
  pet?: Pet;
  /** 关联增项服务列表 */
  additionalServices?: AdditionalService[];
  /** 追加服务订单列表 */
  additionalServiceOrders?: AdditionalServiceOrder[];
}

@Table({ tableName: 'order_details', timestamps: true, comment: '订单明细表' })
export class OrderDetail
  extends Model<OrderDetailAttributes>
  implements OrderDetailAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '明细ID',
  })
  id: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '关联订单ID',
  })
  @ForeignKey(() => Order)
  orderId: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '关联服务ID',
  })
  @ForeignKey(() => Service)
  serviceId: number;

  @Column({
    type: DataType.STRING(100),
    allowNull: false,
    comment: '服务名称，确保删除服务后订单明细的服务名称不丢失',
  })
  serviceName: string;

  @Column({
    type: DataType.DECIMAL(8, 2),
    allowNull: false,
    comment: '服务基础价格，确保服务价格变更后订单明细的价格不受影响',
  })
  servicePrice: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '关联宠物ID',
  })
  @ForeignKey(() => Pet)
  petId?: number;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: '宠物名称，确保删除宠物后订单明细的宠物名称不丢失',
  })
  petName: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: '宠物类型，确保删除宠物后订单明细的宠物类型不丢失',
  })
  petType: string;

  @Column({
    type: DataType.STRING(50),
    comment: '宠物品种，确保删除宠物后订单明细的宠物品种不丢失',
  })
  petBreed?: string;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    comment: '下单时间',
  })
  orderTime: Date;

  @Column({
    type: DataType.STRING(20),
    defaultValue: '待接单',
    comment: '状态',
  })
  status: string;

  @Column({
    type: DataType.STRING(255),
    allowNull: true,
    comment: '用户备注',
  })
  userRemark?: string;

  @BelongsTo(() => Order, { onDelete: 'CASCADE' })
  order: Order;

  @BelongsTo(() => Service, {
    // 存在关联的订单明细时，禁止删除服务
    onDelete: 'NO ACTION',
  })
  service: Service;

  @BelongsTo(() => Pet, {
    onDelete: 'SET NULL',
  })
  pet: Pet;

  @BelongsToMany(() => AdditionalService, () => OrderDetailAdditional)
  additionalServices: AdditionalService[];

  @HasMany(() => AdditionalServiceOrder)
  additionalServiceOrders: AdditionalServiceOrder[];

  @HasMany(() => ServiceDurationRecord)
  serviceDurationRecords: ServiceDurationRecord[];
}
