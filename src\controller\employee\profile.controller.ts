import { Body, Controller, Inject, Put } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { EmployeeService } from '../../service/employee.service';
import { Employee } from '../../entity';
import { CustomError } from '../../error/custom.error';

@Controller('/employee/profile')
export class EmployeeProfileController {
  @Inject()
  ctx: Context;

  @Inject()
  employeeService: EmployeeService;

  @Put('/', { summary: '员工端修改个人信息' })
  async updateProfile(@Body() body: { name?: string; avatar?: string }) {
    // 获取当前登录的员工信息
    const currentUser = this.ctx.state.user as TokenPayload;
    console.log('currentUser', currentUser);
    if (!currentUser || currentUser.userType !== 'employee') {
      throw new CustomError('请先登录员工账号');
    }

    const { name, avatar } = body;

    // 验证参数
    if (!name && !avatar) {
      throw new CustomError('请提供要修改的信息');
    }

    // 验证昵称长度
    if (name && (name.length < 1 || name.length > 20)) {
      throw new CustomError('昵称长度应在1-20个字符之间');
    }

    // 验证头像URL格式
    if (avatar && !avatar.startsWith('http')) {
      throw new CustomError('头像必须是有效的URL地址');
    }

    // 构建更新数据
    const updateData: any = {};
    if (name) {
      updateData.name = name;
    }
    if (avatar) {
      updateData.avatar = avatar;
    }

    // 更新员工信息
    await this.employeeService.update({ id: currentUser.userId }, updateData);

    // 返回更新后的员工信息
    const updatedEmployee = await Employee.findByPk(currentUser.userId, {
      attributes: ['id', 'name', 'avatar', 'phone', 'position', 'rating'],
    });

    if (!updatedEmployee) {
      throw new CustomError('员工信息不存在');
    }

    return {
      id: updatedEmployee.id,
      name: updatedEmployee.name,
      avatar: updatedEmployee.avatar,
      phone: updatedEmployee.phone,
      position: updatedEmployee.position,
      rating: updatedEmployee.rating,
    };
  }
}
