import { Rule, RuleType } from '@midwayjs/validate';

/**
 * 收入统计查询基础DTO
 */
export class RevenueStatisticsQueryDTO {
  @Rule(RuleType.string().optional())
  startDate?: string;

  @Rule(RuleType.string().optional())
  endDate?: string;
}

/**
 * 收入趋势统计查询DTO
 */
export class RevenueTrendQueryDTO extends RevenueStatisticsQueryDTO {
  @Rule(RuleType.string().valid('day', 'week', 'month').default('day'))
  periodType?: 'day' | 'week' | 'month';
}

/**
 * 服务收入统计查询DTO
 */
export class ServiceRevenueQueryDTO extends RevenueStatisticsQueryDTO {
  @Rule(RuleType.number().integer().positive().optional())
  serviceTypeId?: number;

  @Rule(RuleType.number().integer().positive().optional())
  serviceId?: number;

  @Rule(RuleType.number().integer().min(1).default(1))
  page?: number;

  @Rule(RuleType.number().integer().min(1).max(100).default(20))
  pageSize?: number;

  @Rule(RuleType.string().valid('totalRevenue', 'orderCount', 'avgRevenue').default('totalRevenue'))
  sortBy?: 'totalRevenue' | 'orderCount' | 'avgRevenue';

  @Rule(RuleType.string().valid('asc', 'desc').default('desc'))
  sortOrder?: 'asc' | 'desc';
}

/**
 * 员工收入统计查询DTO
 */
export class EmployeeRevenueQueryDTO extends RevenueStatisticsQueryDTO {
  @Rule(RuleType.number().integer().positive().optional())
  employeeId?: number;

  @Rule(RuleType.number().integer().min(1).default(1))
  page?: number;

  @Rule(RuleType.number().integer().min(1).max(100).default(20))
  pageSize?: number;

  @Rule(RuleType.string().valid('totalRevenue', 'orderCount', 'avgRevenue').default('totalRevenue'))
  sortBy?: 'totalRevenue' | 'orderCount' | 'avgRevenue';

  @Rule(RuleType.string().valid('asc', 'desc').default('desc'))
  sortOrder?: 'asc' | 'desc';
}
