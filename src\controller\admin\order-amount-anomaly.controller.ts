import { Controller, Get, Post, Inject, Query, Body, Param } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { OrderAmountAnomalyService } from '../../service/order-amount-anomaly.service';
import { CustomError } from '../../error/custom.error';

@Controller('/admin/order-amount-anomalies')
export class AdminOrderAmountAnomalyController {
  @Inject()
  ctx: Context;

  @Inject()
  orderAmountAnomalyService: OrderAmountAnomalyService;

  @Get('/check', { summary: '检查订单金额异常并生成修复建议' })
  async checkAnomaliesWithSuggestions(
    @Query('clearExistingRecords') clearExistingRecords?: string,
    @Query('orderId') orderId?: number
  ) {
    return await this.orderAmountAnomalyService.generateFixSuggestions({
      clearExistingRecords: clearExistingRecords === 'true',
      orderId: orderId ? parseInt(orderId.toString()) : undefined,
    });
  }

  @Get('/debug-order/:orderId', { summary: '调试订单数据（临时接口）' })
  async debugOrder(@Query('orderId') orderId: number) {
    if (!orderId) {
      throw new CustomError('订单ID不能为空');
    }

    // 查询订单详细信息
    const order = await this.orderAmountAnomalyService.debugOrderData(orderId);
    return order;
  }

  @Get('/debug-check/:orderId', { summary: '调试检查订单异常（临时接口）' })
  async debugCheck(@Param('orderId') orderIdStr: string) {
    const orderId = parseInt(orderIdStr);
    if (!orderId || isNaN(orderId)) {
      throw new CustomError('订单ID不能为空或格式不正确');
    }

    console.log(`开始调试检查订单 ${orderId}`);

    // 直接调用检查方法，查看控制台输出
    const result = await this.orderAmountAnomalyService.generateFixSuggestions({
      clearExistingRecords: false,
      orderId: orderId,
    });

    return {
      message: '检查完成，请查看控制台输出',
      orderId: orderId,
      result: result,
    };
  }

  @Post('/apply-fix', { summary: '应用选定的修复方案' })
  async applyFixSuggestion(
    @Body()
    body: {
      orderId: number;
      fixType: 'fix_original_price' | 'fix_total_fee' | 'fix_discount';
      value?: number;
      cardDeduction?: number;
      couponDeduction?: number;
      operatorId: number;
      operatorName: string;
      remark?: string;
      confirmRisk?: boolean; // 对于高风险操作需要确认
    }
  ) {
    const {
      orderId,
      fixType,
      operatorId,
      operatorName,
      confirmRisk = false,
    } = body;

    if (!orderId || !fixType || !operatorId || !operatorName) {
      throw new CustomError('参数不完整');
    }

    // 对于修改实付金额的高风险操作，需要确认
    if (fixType === 'fix_total_fee' && !confirmRisk) {
      return {
        error: '高风险操作需要确认',
        message:
          '修改实付金额与银行流水相关，请设置 confirmRisk: true 确认操作',
        warning: '请确保银行实际扣款金额与修改后的金额一致',
      };
    }

    return await this.orderAmountAnomalyService.applyFixSuggestion(body);
  }
}
