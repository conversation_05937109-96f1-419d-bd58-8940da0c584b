import { Provide, Inject } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { ILogger } from '@midwayjs/logger';
import { BaseService } from '../common/BaseService';
import { CustomError } from '../error/custom.error';
import {
  ServiceDurationRecord,
  ServiceDurationRecordType,
} from '../entity/service-duration-record.entity';
import { Order } from '../entity/order.entity';
import { OrderDetail } from '../entity/order-detail.entity';
import { Service } from '../entity/service.entity';
import { AdditionalService } from '../entity/additional-service.entity';
import { AdditionalServiceOrder } from '../entity/additional-service-order.entity';
import { AdditionalServiceOrderDetail } from '../entity/additional-service-order-detail.entity';
import { OrderDetailAdditional } from '../entity/order-detail-additional.entity';
import { Employee } from '../entity/employee.entity';
import { OrderStatus } from '../common/Constant';
import { Op } from 'sequelize';
import { OrderDurationCalculatorService } from './order-duration-calculator.service';
import { OrderService } from './order.service';

/**
 * 开始服务接口数据
 */
export interface StartServiceData {
  /** 订单ID */
  orderId: number;
  /** 订单详情ID（主服务时使用） */
  orderDetailId?: number;
  /** 追加服务订单ID（增项服务时使用） */
  additionalServiceOrderId?: number;
  /** 记录类型 */
  recordType: ServiceDurationRecordType;
  /** 服务ID（主服务时使用） */
  serviceId?: number;
  /** 增项服务ID（增项服务时使用） */
  additionalServiceId?: number;
  /** 备注 */
  remark?: string;
}

/**
 * 结束服务接口数据
 */
export interface EndServiceData {
  /** 服务时长记录ID */
  recordId: number;
  /** 备注 */
  remark?: string;
}

@Provide()
export class ServiceDurationRecordService extends BaseService<ServiceDurationRecord> {
  @Inject()
  ctx: Context;

  @Inject()
  logger: ILogger;

  @Inject()
  orderDurationCalculatorService: OrderDurationCalculatorService;

  @Inject()
  orderService: OrderService;

  constructor() {
    super('服务时长记录');
  }

  getModel() {
    return ServiceDurationRecord;
  }

  /**
   * 开始单个服务项目
   * 用于员工手动开始特定的主服务或增项服务
   *
   * @param employeeId 员工ID
   * @param data 开始服务的数据
   * @returns 创建的服务时长记录
   */
  async startService(employeeId: number, data: StartServiceData) {
    const {
      orderId,
      orderDetailId,
      additionalServiceOrderId,
      recordType,
      serviceId,
      additionalServiceId,
      remark,
    } = data;

    // 1. 验证订单状态和权限
    const order = await Order.findByPk(orderId, {
      include: [Employee],
    });

    if (!order) {
      throw new CustomError('订单不存在');
    }

    if (order.status !== OrderStatus.服务中) {
      throw new CustomError('只有服务中的订单才能开始服务');
    }

    if (order.employeeId !== employeeId) {
      throw new CustomError('无权限操作此订单');
    }

    // 2. 检查是否已有未结束的完全相同的服务记录（防止重复开始）
    // 构建精确的查询条件，确保只有完全相同的服务才被认为是重复
    const whereCondition: any = {
      orderId,
      employeeId,
      recordType,
      endTime: null, // 查找未结束的记录
    };

    // 根据服务类型构建精确的唯一标识
    if (recordType === ServiceDurationRecordType.MAIN_SERVICE) {
      // 主服务：orderDetailId + serviceId 唯一标识
      whereCondition.orderDetailId = orderDetailId;
      whereCondition.serviceId = serviceId;
      whereCondition.additionalServiceOrderId = null;
      whereCondition.additionalServiceId = null;
    } else if (recordType === ServiceDurationRecordType.ADDITIONAL_SERVICE) {
      // 增项服务：需要区分主订单中的增项服务和追加服务中的增项服务
      if (additionalServiceOrderId) {
        // 追加服务中的增项服务：additionalServiceOrderId + additionalServiceId 唯一标识
        whereCondition.additionalServiceOrderId = additionalServiceOrderId;
        whereCondition.additionalServiceId = additionalServiceId;
        whereCondition.orderDetailId = null;
        whereCondition.serviceId = null;
      } else {
        // 主订单中的增项服务：orderDetailId + additionalServiceId 唯一标识
        whereCondition.orderDetailId = orderDetailId;
        whereCondition.additionalServiceId = additionalServiceId;
        whereCondition.additionalServiceOrderId = null;
        whereCondition.serviceId = null;
      }
    }

    const existingRecord = await ServiceDurationRecord.findOne({
      where: whereCondition,
    });

    if (existingRecord) {
      // 检查现有记录的状态
      if (existingRecord.startTime) {
        // 如果已经有开始时间，说明服务真的已经开始了，不能重复开始
        let serviceDescription = '';
        if (recordType === ServiceDurationRecordType.MAIN_SERVICE) {
          serviceDescription = `主服务(ID:${serviceId})`;
        } else if (additionalServiceOrderId) {
          serviceDescription = `追加服务中的增项服务(追加订单ID:${additionalServiceOrderId}, 增项服务ID:${additionalServiceId})`;
        } else {
          serviceDescription = `主订单中的增项服务(订单详情ID:${orderDetailId}, 增项服务ID:${additionalServiceId})`;
        }

        throw new CustomError(
          `${serviceDescription}已开始，请先结束当前服务再重新开始`
        );
      } else {
        // 如果没有开始时间，说明是无效记录，删除它并继续创建新记录
        this.logger.info(
          `发现无效的服务记录(ID:${existingRecord.id})，startTime为null，将删除并重新创建`
        );
        await existingRecord.destroy();
      }
    }

    // 3. 根据服务类型获取服务信息并验证
    let serviceName = '';
    let additionalServiceName = '';

    if (recordType === ServiceDurationRecordType.MAIN_SERVICE && serviceId) {
      // 主服务：验证服务存在性
      const service = await Service.findByPk(serviceId);
      if (!service) {
        throw new CustomError('服务不存在');
      }
      serviceName = service.serviceName;
    } else if (
      recordType === ServiceDurationRecordType.ADDITIONAL_SERVICE &&
      additionalServiceId
    ) {
      // 增项服务：需要区分主订单中的增项服务和追加服务中的增项服务
      const additionalService = await AdditionalService.findByPk(
        additionalServiceId
      );
      if (!additionalService) {
        throw new CustomError('增项服务不存在');
      }

      // 检查是否需要统计时长（只有需要统计时长的增项服务才能开始计时）
      if (!additionalService.needDurationTracking) {
        throw new CustomError('该增项服务不需要统计时长');
      }

      if (additionalServiceOrderId) {
        // 情况1：追加服务中的增项服务
        const additionalServiceOrder = await AdditionalServiceOrder.findByPk(
          additionalServiceOrderId
        );
        if (!additionalServiceOrder) {
          throw new CustomError('追加服务订单不存在');
        }

        // 验证追加服务订单状态（只有已支付的追加服务才能开始计时）
        if (additionalServiceOrder.status !== 'paid') {
          throw new CustomError('只有已支付的追加服务才能开始计时');
        }

        // 验证追加服务订单是否包含该增项服务
        const orderDetailExists = await AdditionalServiceOrderDetail.findOne({
          where: {
            additionalServiceOrderId,
            serviceId: additionalServiceId,
          },
        });

        if (!orderDetailExists) {
          throw new CustomError('该追加服务订单中不包含指定的增项服务');
        }

        this.logger.info(
          `开始追加服务中的增项服务: 订单${orderId}, 追加服务订单${additionalServiceOrderId}, 增项服务${additionalServiceId}`
        );
      } else {
        // 情况2：主订单中的增项服务
        if (!orderDetailId) {
          throw new CustomError('主订单中的增项服务必须提供orderDetailId');
        }

        // 验证该订单详情是否包含该增项服务
        const orderDetailAdditional = await OrderDetailAdditional.findOne({
          where: {
            odId: orderDetailId,
            aId: additionalServiceId,
          },
        });

        if (!orderDetailAdditional) {
          throw new CustomError('该订单详情中不包含指定的增项服务');
        }

        this.logger.info(
          `开始主订单中的增项服务: 订单${orderId}, 订单详情${orderDetailId}, 增项服务${additionalServiceId}`
        );
      }

      additionalServiceName = additionalService.name;
      serviceName = additionalService.name; // 统一使用serviceName字段存储
    } else {
      throw new CustomError('服务类型和服务ID不匹配');
    }

    // 4. 创建服务时长记录
    const record = await ServiceDurationRecord.create({
      orderId,
      orderDetailId,
      additionalServiceOrderId: additionalServiceOrderId || null, // 原始增项服务时可能为空
      employeeId,
      recordType,
      serviceId,
      serviceName,
      additionalServiceId,
      additionalServiceName,
      startTime: new Date(), // 记录开始时间
      remark,
    });

    this.logger.info(
      `员工 ${employeeId} 开始服务: 订单${orderId}, 服务类型${recordType}, 服务名称${serviceName}`
    );

    return record;
  }

  /**
   * 结束服务
   * 完成单个服务项目，并检查是否需要自动结束整个订单
   */
  async endService(employeeId: number, data: EndServiceData) {
    const { recordId, remark } = data;

    // 查找服务记录
    const record = await ServiceDurationRecord.findByPk(recordId, {
      include: [Order],
    });

    if (!record) {
      throw new CustomError('服务记录不存在');
    }

    if (record.employeeId !== employeeId) {
      throw new CustomError('无权限操作此服务记录');
    }

    if (record.endTime) {
      throw new CustomError('该服务已结束');
    }

    if (!record.startTime) {
      throw new CustomError('服务尚未开始');
    }

    const endTime = new Date();
    const duration = Math.round(
      (endTime.getTime() - record.startTime.getTime()) / (1000 * 60)
    );

    // 更新服务记录
    await record.update({
      endTime,
      duration,
      remark: remark || record.remark,
    });

    // 异步更新平均时长（不等待完成，避免影响主流程）
    this.updateAverageDuration(
      record.recordType,
      record.serviceId,
      record.additionalServiceId
    ).catch(error => {
      this.logger.error('更新平均时长失败:', error);
    });

    // 异步更新订单总服务时长
    this.orderDurationCalculatorService
      .calculateAndUpdateOrderDuration(record.orderId)
      .catch(error => {
        this.logger.error('更新订单总服务时长失败:', error);
      });

    // 检查是否需要自动结束订单
    this.checkAndCompleteOrderIfAllServicesFinished(
      record.orderId,
      employeeId
    ).catch(error => {
      this.logger.error('检查订单完成状态失败:', error);
    });

    return record;
  }

  /**
   * 更新服务平均时长
   * 基于最近10次服务记录计算平均时长，用于优化服务时间预估
   *
   * @param recordType 记录类型（主服务或增项服务）
   * @param serviceId 主服务ID（主服务时使用）
   * @param additionalServiceId 增项服务ID（增项服务时使用）
   */
  private async updateAverageDuration(
    recordType: ServiceDurationRecordType,
    serviceId?: number,
    additionalServiceId?: number
  ) {
    try {
      if (recordType === ServiceDurationRecordType.MAIN_SERVICE && serviceId) {
        // 处理主服务平均时长更新
        const records = await ServiceDurationRecord.findAll({
          where: {
            recordType: ServiceDurationRecordType.MAIN_SERVICE,
            serviceId,
            duration: { [Op.not]: null }, // 只统计已完成的记录
          },
          order: [['createdAt', 'DESC']], // 按创建时间倒序
          limit: 10, // 取最近10次记录
        });

        if (records.length > 0) {
          // 计算平均时长（四舍五入到分钟）
          const avgDuration = Math.round(
            records.reduce((sum, record) => sum + (record.duration || 0), 0) /
              records.length
          );

          // 更新主服务的平均时长
          await Service.update({ avgDuration }, { where: { id: serviceId } });

          this.logger.info(
            `更新主服务 ${serviceId} 平均时长: ${avgDuration}分钟 (基于${records.length}次记录)`
          );
        }
      } else if (
        recordType === ServiceDurationRecordType.ADDITIONAL_SERVICE &&
        additionalServiceId
      ) {
        // 处理增项服务平均时长更新
        const records = await ServiceDurationRecord.findAll({
          where: {
            recordType: ServiceDurationRecordType.ADDITIONAL_SERVICE,
            additionalServiceId,
            duration: { [Op.not]: null }, // 只统计已完成的记录
          },
          order: [['createdAt', 'DESC']], // 按创建时间倒序
          limit: 10, // 取最近10次记录
        });

        if (records.length > 0) {
          // 计算平均时长（四舍五入到分钟）
          const avgDuration = Math.round(
            records.reduce((sum, record) => sum + (record.duration || 0), 0) /
              records.length
          );

          // 更新增项服务的时长字段
          await AdditionalService.update(
            { duration: avgDuration },
            { where: { id: additionalServiceId } }
          );

          this.logger.info(
            `更新增项服务 ${additionalServiceId} 平均时长: ${avgDuration}分钟 (基于${records.length}次记录)`
          );
        }
      }
    } catch (error) {
      this.logger.error('更新平均时长失败:', error);
    }
  }

  /**
   * 查询服务时长记录列表
   */
  async getServiceDurationRecords(orderId: number, employeeId?: number) {
    const where: any = { orderId };
    if (employeeId) {
      where.employeeId = employeeId;
    }

    const records = await ServiceDurationRecord.findAll({
      where,
      include: [
        {
          model: Employee,
          attributes: ['id', 'name', 'phone'],
        },
        {
          model: Service,
          attributes: ['id', 'serviceName', 'avgDuration'],
        },
        {
          model: AdditionalService,
          attributes: [
            'id',
            'name',
            'duration',
            'price',
            'needDurationTracking',
          ],
        },
        {
          model: OrderDetail,
          attributes: ['id', 'serviceName', 'petName', 'servicePrice'],
        },
        {
          model: AdditionalServiceOrder,
          attributes: ['id', 'totalFee', 'status', 'originalPrice'],
        },
      ],
      order: [['startTime', 'ASC']],
    });

    // 计算统计信息
    const totalRecords = records.length;
    const completedRecords = records.filter(record => record.endTime).length;
    const inProgressRecords = records.filter(
      record => record.startTime && !record.endTime
    ).length;

    // 按类型分组
    const mainServiceRecords = records.filter(
      r => r.recordType === 'main_service'
    );
    const additionalServiceRecords = records.filter(
      r => r.recordType === 'additional_service'
    );

    // 计算总时长
    const totalDuration = records
      .filter(r => r.duration)
      .reduce((sum, r) => sum + (r.duration || 0), 0);

    const mainServiceDuration = mainServiceRecords
      .filter(r => r.duration)
      .reduce((sum, r) => sum + (r.duration || 0), 0);

    const additionalServiceDuration = additionalServiceRecords
      .filter(r => r.duration)
      .reduce((sum, r) => sum + (r.duration || 0), 0);

    return {
      orderId,
      records,
      statistics: {
        totalRecords,
        completedRecords,
        inProgressRecords,
        totalDuration,
        mainServiceDuration,
        additionalServiceDuration,
        mainServiceCount: mainServiceRecords.length,
        additionalServiceCount: additionalServiceRecords.length,
      },
    };
  }

  /**
   * 获取员工当前进行中的服务
   */
  async getCurrentServices(employeeId: number) {
    const records = await ServiceDurationRecord.findAll({
      where: {
        employeeId,
        endTime: null,
      },
      include: [
        {
          model: Order,
          attributes: ['id', 'sn', 'status'],
          include: [
            {
              model: require('../entity/customer.entity').Customer,
              attributes: ['id', 'nickname', 'phone'],
            },
          ],
        },
        {
          model: Service,
          attributes: ['id', 'serviceName', 'avgDuration'],
        },
        {
          model: AdditionalService,
          attributes: [
            'id',
            'name',
            'duration',
            'price',
            'needDurationTracking',
          ],
        },
        {
          model: OrderDetail,
          attributes: ['id', 'serviceName', 'petName', 'servicePrice'],
        },
        {
          model: AdditionalServiceOrder,
          attributes: ['id', 'totalFee', 'status', 'originalPrice'],
        },
      ],
      order: [['startTime', 'ASC']],
    });

    // 计算每个服务已进行的时长
    const currentTime = new Date();
    const recordsWithDuration = records.map(record => {
      let currentDuration = 0;
      if (record.startTime) {
        currentDuration = Math.round(
          (currentTime.getTime() - record.startTime.getTime()) / (1000 * 60)
        );
      }

      return {
        ...record.toJSON(),
        currentDuration, // 当前已进行的时长（分钟）
        expectedDuration:
          record.recordType === 'main_service'
            ? record.service?.avgDuration
            : record.additionalService?.duration, // 预期时长
      };
    });

    return {
      employeeId,
      currentServices: recordsWithDuration,
      totalCurrentServices: records.length,
    };
  }

  /**
   * 开始整体订单服务
   * 这是服务流程的入口方法，执行以下操作：
   * 1. 将订单状态从"已出发"或"待服务"更新为"服务中"
   * 2. 记录实际服务开始时间
   * 3. 自动开始所有主服务项目的计时
   * 4. 上传服务前照片（可选）
   *
   * 注意：增项服务需要员工手动开始，且只有needDurationTracking=true的增项服务才需要计时
   *
   * @param employeeId 员工ID
   * @param orderId 订单ID
   * @param beforePhotos 服务前照片数组（可选）
   * @returns 开始服务的结果信息
   */
  async startOrderService(
    employeeId: number,
    orderId: number,
    beforePhotos?: string[]
  ) {
    // 1. 验证订单状态和权限
    const order = await Order.findByPk(orderId, {
      include: [
        {
          model: OrderDetail,
          include: [Service],
        },
        Employee,
      ],
    });

    if (!order) {
      throw new CustomError('订单不存在');
    }

    if (order.employeeId !== employeeId) {
      throw new CustomError('无权限操作此订单');
    }

    // 检查订单状态，只允许从"已出发"或"待服务"状态开始服务
    if (
      ![OrderStatus.已出发, OrderStatus.待服务].includes(
        order.status as OrderStatus
      )
    ) {
      throw new CustomError('订单状态不正确，无法开始服务');
    }

    // 2. 更新订单状态为"服务中"并记录开始时间
    const actualServiceStartTime = new Date();
    await this.orderService.order_start(orderId, employeeId, beforePhotos);

    // 3. 自动开始所有主服务项目的计时
    const mainServiceRecords = [];
    for (const orderDetail of order.orderDetails || []) {
      // 检查是否已有未结束的主服务记录（防止重复创建）
      const existingRecord = await ServiceDurationRecord.findOne({
        where: {
          orderId,
          employeeId,
          recordType: ServiceDurationRecordType.MAIN_SERVICE,
          orderDetailId: orderDetail.id,
          serviceId: orderDetail.serviceId,
          endTime: null, // 查找未结束的记录
        },
      });

      if (!existingRecord) {
        // 为每个主服务创建时长记录
        const record = await ServiceDurationRecord.create({
          orderId,
          orderDetailId: orderDetail.id,
          employeeId,
          recordType: ServiceDurationRecordType.MAIN_SERVICE,
          serviceId: orderDetail.serviceId,
          serviceName: orderDetail.serviceName,
          startTime: actualServiceStartTime, // 所有主服务使用相同的开始时间
          remark: '自动开始主服务',
        });

        mainServiceRecords.push(record);
      }
    }

    this.logger.info(
      `员工 ${employeeId} 开始订单 ${orderId} 服务，自动开始了 ${mainServiceRecords.length} 个主服务项目`
    );

    return {
      orderId,
      orderStatus: OrderStatus.服务中,
      actualServiceStartTime,
      mainServiceRecords,
      message: '成功开始订单服务',
    };
  }

  /**
   * 检查订单是否所有服务都已完成，如果是则自动结束订单
   * 规则：
   * 1. 所有主服务必须完成
   * 2. 所有需要统计时长的增项服务必须完成
   * 3. 不需要统计时长的增项服务不影响订单完成状态
   * 4. 不能存在未付款的附加服务
   */
  private async checkAndCompleteOrderIfAllServicesFinished(
    orderId: number,
    employeeId: number
  ): Promise<boolean> {
    try {
      // 获取订单信息
      const order = await Order.findByPk(orderId, {
        include: [
          {
            model: OrderDetail,
            include: [Service],
          },
        ],
      });

      if (!order || order.status !== OrderStatus.服务中) {
        return false;
      }

      // 检查所有主服务是否完成
      const allMainServicesCompleted = await this.checkAllMainServicesCompleted(
        orderId
      );
      if (!allMainServicesCompleted) {
        this.logger.info(`订单 ${orderId} 主服务尚未全部完成`);
        return false;
      }

      // 检查是否存在未付款的附加服务（必须条件）
      // 使用 orderService 的方法确保逻辑一致性
      const hasUnpaidAdditionalServices =
        await this.orderService.checkUnpaidAdditionalServices(orderId);
      if (hasUnpaidAdditionalServices) {
        this.logger.info(`订单 ${orderId} 存在未付款的附加服务，无法结束订单`);
        return false;
      }

      // 检查所有需要统计时长的增项服务是否完成（必须条件）
      const allRequiredAdditionalServicesCompleted =
        await this.checkAllRequiredAdditionalServicesCompleted(orderId);
      if (!allRequiredAdditionalServicesCompleted) {
        this.logger.info(`订单 ${orderId} 需要统计时长的增项服务尚未全部完成`);
        return false;
      }

      // 所有服务都已完成，自动结束订单
      this.logger.info(`订单 ${orderId} 所有服务项目已完成，自动结束订单`);
      await this.orderService.order_complete(orderId, employeeId);

      return true;
    } catch (error) {
      this.logger.error(`检查订单 ${orderId} 完成状态失败:`, error);
      return false;
    }
  }

  /**
   * 检查所有主服务是否已完成
   */
  private async checkAllMainServicesCompleted(
    orderId: number
  ): Promise<boolean> {
    // 获取订单的所有主服务项目
    const orderDetails = await OrderDetail.findAll({
      where: { orderId },
      include: [Service],
    });

    if (orderDetails.length === 0) {
      return true; // 没有主服务，认为已完成
    }

    // 检查每个主服务是否都有对应的完成记录
    for (const orderDetail of orderDetails) {
      const completedRecord = await ServiceDurationRecord.findOne({
        where: {
          orderId,
          orderDetailId: orderDetail.id,
          serviceId: orderDetail.serviceId,
          recordType: ServiceDurationRecordType.MAIN_SERVICE,
          endTime: { [Op.not]: null }, // 必须有结束时间
        },
      });

      if (!completedRecord) {
        return false; // 有主服务未完成
      }
    }

    return true; // 所有主服务都已完成
  }

  /**
   * 检查所有需要统计时长的增项服务是否已完成
   * 规则：
   * 1. 所有需要统计时长的增项服务都必须有完成记录（endTime 不为空）
   * 2. 不能存在未开始但需要统计时长的增项服务
   */
  private async checkAllRequiredAdditionalServicesCompleted(
    orderId: number
  ): Promise<boolean> {
    // 1. 检查主订单中的增项服务
    const orderDetails = await OrderDetail.findAll({
      where: { orderId },
      include: [
        {
          model: AdditionalService,
          through: { attributes: [] }, // 排除中间表字段
          attributes: ['id', 'name', 'needDurationTracking'],
          required: false,
        },
      ],
    });

    // 收集主订单中需要统计时长的增项服务
    const requiredMainOrderAdditionalServices = [];
    for (const orderDetail of orderDetails) {
      for (const additionalService of orderDetail.additionalServices || []) {
        if (additionalService.needDurationTracking) {
          requiredMainOrderAdditionalServices.push({
            orderDetailId: orderDetail.id,
            additionalServiceId: additionalService.id,
            additionalServiceName: additionalService.name,
          });
        }
      }
    }

    // 检查主订单中的增项服务是否都已完成
    for (const service of requiredMainOrderAdditionalServices) {
      const serviceRecord = await ServiceDurationRecord.findOne({
        where: {
          orderId,
          orderDetailId: service.orderDetailId,
          additionalServiceId: service.additionalServiceId,
          recordType: ServiceDurationRecordType.ADDITIONAL_SERVICE,
        },
      });

      if (!serviceRecord) {
        return false;
      }

      if (!serviceRecord.endTime) {
        return false;
      }
    }

    // 2. 检查追加服务订单中的增项服务
    const additionalServiceOrders = await AdditionalServiceOrder.findAll({
      where: {
        orderDetailId: orderDetails.map(od => od.id),
        status: 'paid', // 只检查已支付的追加服务
      },
      include: [
        {
          model: require('../entity/additional-service-order-detail.entity')
            .AdditionalServiceOrderDetail,
          as: 'details',
          required: false,
        },
      ],
    });

    // 收集追加服务中需要统计时长的增项服务
    const requiredAdditionalServiceOrderServices = [];
    for (const additionalServiceOrder of additionalServiceOrders) {
      for (const detail of additionalServiceOrder.details || []) {
        // 在追加服务订单详情中，serviceId 实际上是 AdditionalService 的 ID
        const additionalService = await AdditionalService.findByPk(
          detail.serviceId
        );
        if (additionalService && additionalService.needDurationTracking) {
          requiredAdditionalServiceOrderServices.push({
            additionalServiceOrderId: additionalServiceOrder.id,
            additionalServiceId: additionalService.id,
            additionalServiceName: additionalService.name,
          });
        }
      }
    }

    // 检查追加服务中的增项服务是否都已完成
    for (const service of requiredAdditionalServiceOrderServices) {
      const serviceRecord = await ServiceDurationRecord.findOne({
        where: {
          orderId,
          additionalServiceOrderId: service.additionalServiceOrderId,
          additionalServiceId: service.additionalServiceId,
          recordType: ServiceDurationRecordType.ADDITIONAL_SERVICE,
        },
      });

      if (!serviceRecord) {
        return false;
      }

      if (!serviceRecord.endTime) {
        return false;
      }
    }

    return true; // 所有需要统计时长的增项服务都已完成
  }

  /**
   * 查询订单服务状态和所有服务项目
   * 返回主服务和所有增项服务的完整信息
   */
  async getOrderServiceStatus(orderId: number, employeeId?: number) {
    // 查询订单基本信息
    const order = await Order.findByPk(orderId, {
      include: [
        {
          model: OrderDetail,
          include: [
            {
              model: Service,
              attributes: ['id', 'serviceName', 'avgDuration'],
            },
            {
              model: require('../entity/additional-service-order.entity')
                .AdditionalServiceOrder,
              include: [
                {
                  model:
                    require('../entity/additional-service-order-detail.entity')
                      .AdditionalServiceOrderDetail,
                  include: [
                    {
                      model: Service,
                      attributes: ['id', 'serviceName'],
                    },
                  ],
                },
              ],
            },
          ],
        },
        {
          model: require('../entity/customer.entity').Customer,
          attributes: ['id', 'nickname', 'phone'],
        },
        {
          model: Employee,
          attributes: ['id', 'name', 'phone'],
        },
      ],
    });

    if (!order) {
      throw new CustomError('订单不存在');
    }

    // 如果指定了员工ID，验证权限
    if (employeeId && order.employeeId !== employeeId) {
      throw new CustomError('无权限查看此订单');
    }

    // 查询所有服务时长记录
    const serviceRecords = await this.getServiceDurationRecords(
      orderId,
      employeeId
    );

    // 整理主服务信息
    const mainServices = [];
    for (const orderDetail of order.orderDetails || []) {
      const mainServiceRecord = serviceRecords.records.find(
        r =>
          r.recordType === 'main_service' &&
          r.orderDetailId === orderDetail.id &&
          r.serviceId === orderDetail.serviceId
      );

      mainServices.push({
        orderDetailId: orderDetail.id,
        serviceId: orderDetail.serviceId,
        serviceName: orderDetail.serviceName,
        servicePrice: orderDetail.servicePrice,
        petName: orderDetail.petName,
        petType: orderDetail.petType,
        petBreed: orderDetail.petBreed,
        avgDuration: orderDetail.service?.avgDuration,
        serviceRecord: mainServiceRecord || null,
        status: mainServiceRecord
          ? mainServiceRecord.endTime
            ? 'completed'
            : 'in_progress'
          : 'not_started',
      });
    }

    // 整理增项服务信息
    const additionalServices = [];
    for (const orderDetail of order.orderDetails || []) {
      for (const additionalServiceOrder of orderDetail.additionalServiceOrders ||
        []) {
        for (const detail of additionalServiceOrder.details || []) {
          const additionalServiceRecord = serviceRecords.records.find(
            r =>
              r.recordType === 'additional_service' &&
              r.additionalServiceOrderId === additionalServiceOrder.id &&
              r.serviceId === detail.serviceId
          );

          additionalServices.push({
            additionalServiceOrderId: additionalServiceOrder.id,
            additionalServiceOrderSn: additionalServiceOrder.sn,
            additionalServiceOrderStatus: additionalServiceOrder.status,
            serviceId: detail.serviceId,
            serviceName: detail.serviceName,
            servicePrice: detail.servicePrice,
            quantity: detail.quantity,
            serviceRecord: additionalServiceRecord || null,
            status: additionalServiceRecord
              ? additionalServiceRecord.endTime
                ? 'completed'
                : 'in_progress'
              : 'not_started',
          });
        }
      }
    }

    return {
      orderId,
      orderSn: order.sn,
      orderStatus: order.status,
      actualServiceStartTime: order.actualServiceStartTime,
      actualServiceEndTime: order.actualServiceEndTime,
      actualServiceDuration: order.actualServiceDuration,
      customer: order.customer,
      employee: order.employee,
      mainServices,
      additionalServices,
      serviceStatistics: serviceRecords.statistics,
      summary: {
        totalMainServices: mainServices.length,
        totalAdditionalServices: additionalServices.length,
        completedMainServices: mainServices.filter(
          s => s.status === 'completed'
        ).length,
        completedAdditionalServices: additionalServices.filter(
          s => s.status === 'completed'
        ).length,
        inProgressMainServices: mainServices.filter(
          s => s.status === 'in_progress'
        ).length,
        inProgressAdditionalServices: additionalServices.filter(
          s => s.status === 'in_progress'
        ).length,
      },
    };
  }
}
