import {
  Table,
  Column,
  Model,
  DataType,
  BelongsTo,
  ForeignKey,
} from 'sequelize-typescript';
import { Order } from './order.entity';
import { OrderDetail } from './order-detail.entity';
import { Service } from './service.entity';
import { AdditionalService } from './additional-service.entity';
import { AdditionalServiceOrder } from './additional-service-order.entity';
import { Employee } from './employee.entity';

/**
 * 服务时长记录类型枚举
 */
export enum ServiceDurationRecordType {
  /** 主服务 */
  MAIN_SERVICE = 'main_service',
  /** 增项服务 */
  ADDITIONAL_SERVICE = 'additional_service',
}

/**
 * 服务时长记录属性接口
 */
export interface ServiceDurationRecordAttributes {
  /** 记录ID */
  id: number;
  /** 关联订单ID */
  orderId: number;
  /** 关联订单详情ID */
  orderDetailId?: number;
  /** 关联追加服务订单ID */
  additionalServiceOrderId?: number;
  /** 关联员工ID */
  employeeId: number;
  /** 记录类型 */
  recordType: ServiceDurationRecordType;
  /** 关联服务ID（主服务时使用） */
  serviceId?: number;
  /** 服务名称（冗余字段） */
  serviceName: string;
  /** 关联增项服务ID（增项服务时使用） */
  additionalServiceId?: number;
  /** 增项服务名称（冗余字段） */
  additionalServiceName?: string;
  /** 服务开始时间 */
  startTime?: Date;
  /** 服务结束时间 */
  endTime?: Date;
  /** 服务时长（分钟） */
  duration?: number;
  /** 备注 */
  remark?: string;
  /** 创建时间 */
  createdAt?: Date;
  /** 更新时间 */
  updatedAt?: Date;
  /** 关联的订单 */
  order?: Order;
  /** 关联的订单详情 */
  orderDetail?: OrderDetail;
  /** 关联的追加服务订单 */
  additionalServiceOrder?: AdditionalServiceOrder;
  /** 关联的员工 */
  employee?: Employee;
  /** 关联的服务 */
  service?: Service;
  /** 关联的增项服务 */
  additionalService?: AdditionalService;
}

/**
 * 服务时长记录表
 */
@Table({
  tableName: 'service_duration_records',
  timestamps: true,
  comment: '服务时长记录表',
})
export class ServiceDurationRecord
  extends Model<ServiceDurationRecordAttributes>
  implements ServiceDurationRecordAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '记录ID',
  })
  id: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '关联订单ID',
  })
  @ForeignKey(() => Order)
  orderId: number;

  @Column({
    type: DataType.INTEGER,
    comment: '关联订单详情ID',
  })
  @ForeignKey(() => OrderDetail)
  orderDetailId?: number;

  @Column({
    type: DataType.INTEGER,
    comment: '关联追加服务订单ID',
  })
  @ForeignKey(() => AdditionalServiceOrder)
  additionalServiceOrderId?: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '关联员工ID',
  })
  @ForeignKey(() => Employee)
  employeeId: number;

  @Column({
    type: DataType.ENUM(...Object.values(ServiceDurationRecordType)),
    allowNull: false,
    comment: '记录类型',
  })
  recordType: ServiceDurationRecordType;

  @Column({
    type: DataType.INTEGER,
    comment: '关联服务ID（主服务时使用）',
  })
  @ForeignKey(() => Service)
  serviceId?: number;

  @Column({
    type: DataType.STRING(100),
    allowNull: false,
    comment: '服务名称（冗余字段）',
  })
  serviceName: string;

  @Column({
    type: DataType.INTEGER,
    comment: '关联增项服务ID（增项服务时使用）',
  })
  @ForeignKey(() => AdditionalService)
  additionalServiceId?: number;

  @Column({
    type: DataType.STRING(100),
    comment: '增项服务名称（冗余字段）',
  })
  additionalServiceName?: string;

  @Column({
    type: DataType.DATE,
    comment: '服务开始时间',
  })
  startTime?: Date;

  @Column({
    type: DataType.DATE,
    comment: '服务结束时间',
  })
  endTime?: Date;

  @Column({
    type: DataType.INTEGER,
    comment: '服务时长（分钟）',
  })
  duration?: number;

  @Column({
    type: DataType.STRING(255),
    comment: '备注',
  })
  remark?: string;

  @BelongsTo(() => Order)
  order?: Order;

  @BelongsTo(() => OrderDetail)
  orderDetail?: OrderDetail;

  @BelongsTo(() => AdditionalServiceOrder)
  additionalServiceOrder?: AdditionalServiceOrder;

  @BelongsTo(() => Employee)
  employee?: Employee;

  @BelongsTo(() => Service)
  service?: Service;

  @BelongsTo(() => AdditionalService)
  additionalService?: AdditionalService;
}
