# 订单金额异常检查接口

## 概述

订单金额异常检查系统提供简化的API接口，专门用于检测和修复订单金额异常，服务于收入统计数据验证。系统能够检测主订单和追加服务的完整金额计算，提供安全的修复建议。

## 核心接口

### 1. 检查异常并生成修复建议

**接口地址：** `GET /admin/order-amount-anomalies/check`

**接口描述：** 一步到位检查订单金额异常并生成详细的修复建议，包含主订单和追加服务的完整计算

**请求参数：**
- `clearExistingRecords` (string, 可选): 是否在检测前清除所有异常记录，默认false
- `orderId` (number, 可选): 指定订单ID，只检查该订单

**响应示例：**
```json
{
  "anomalyCount": 2,
  "suggestions": [
    {
      "orderId": 123,
      "orderSn": "ORD20231201001",
      "anomalyType": "calculation_error",
      "description": "实付金额计算异常，差异20.50元",
      "severity": 3,
      "currentData": {
        "originalPrice": 100.00,
        "totalFee": 59.50,
        "cardDeduction": 20.00,
        "couponDeduction": 0.00
      },
      "calculatedOriginalPrice": 100.00,
      "expectedTotalFee": 80.00,
      "suggestions": [
        {
          "type": "fix_total_fee",
          "title": "修正实付金额（谨慎操作）",
          "description": "将实付金额从 59.50元 修正为 80.00元",
          "action": {
            "field": "totalFee",
            "value": 80.00,
            "reason": "根据原价和优惠重新计算"
          },
          "risk": "high",
          "recommended": false,
          "warning": "⚠️ 实付金额与银行流水相关，修改前请确认银行实际扣款金额"
        },
        {
          "type": "fix_original_price",
          "title": "修正原价",
          "description": "将原价修正为 79.50元",
          "action": {
            "field": "originalPrice",
            "value": 79.50,
            "reason": "根据实付金额和优惠反推原价"
          },
          "risk": "medium",
          "recommended": false
        }
      ],
      "manualReviewRequired": false
    }
  ]
}
```

### 2. 应用修复方案

**接口地址：** `POST /admin/order-amount-anomalies/apply-fix`

**接口描述：** 应用用户选定的修复方案

**请求体：**
```json
{
  "orderId": 123,
  "fixType": "fix_original_price",
  "value": 100.00,
  "operatorId": 1,
  "operatorName": "管理员",
  "remark": "根据订单明细修正原价",
  "confirmRisk": true
}
```

**参数说明：**
- `orderId` (number, 必填): 订单ID
- `fixType` (string, 必填): 修复类型，可选值：
  - `fix_original_price`: 修正原价（推荐）
  - `fix_total_fee`: 修正实付金额（高风险）
  - `fix_discount`: 调整优惠金额
- `value` (number, 可选): 修正值（用于原价和实付金额）
- `cardDeduction` (number, 可选): 权益卡抵扣金额（用于优惠调整）
- `couponDeduction` (number, 可选): 代金券抵扣金额（用于优惠调整）
- `operatorId` (number, 必填): 操作人ID
- `operatorName` (string, 必填): 操作人姓名
- `remark` (string, 可选): 备注说明
- `confirmRisk` (boolean, 可选): 高风险操作确认标志

## 使用流程

### 简化的异常检查和修复流程

```bash
# 1. 检查异常并生成修复建议
GET /admin/order-amount-anomalies/check?clearExistingRecords=true

# 2. 应用修复方案（修正原价 - 推荐）
POST /admin/order-amount-anomalies/apply-fix
{
  "orderId": 123,
  "fixType": "fix_original_price",
  "value": 100.00,
  "operatorId": 1,
  "operatorName": "管理员",
  "remark": "根据订单明细修正原价"
}

# 3. 验证修复效果
GET /admin/revenue-statistics/overview
```

### 修复方案选择原则

- 🟢 **修正原价**: 风险低，推荐优先使用
- 🟡 **调整优惠**: 风险中等，需要确认业务逻辑  
- 🔴 **修正实付**: 风险高，需要确认银行流水，谨慎操作

## 注意事项

1. **数据备份**: 修复前建议备份订单数据
2. **风险控制**: 高风险操作需要额外确认
3. **操作记录**: 所有修复操作都有详细日志
4. **计算逻辑**: 系统会计算主订单和追加服务的完整原价
