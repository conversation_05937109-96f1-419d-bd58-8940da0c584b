import { Provide, Inject, ILogger } from '@midwayjs/core';
import {
  Order,
  OrderDetail,
  Service,
  AdditionalServiceOrder,
  OrderAmountAnomalyRecord,
  OrderAmountFixLog,
  OrderAmountAnomalyType,
  AnomalyProcessStatus,
  FixOperationType,
  FixResult,
} from '../entity';
import { Op, literal, Transaction } from 'sequelize';
import { OrderStatus } from '../common/Constant';

/**
 * 订单金额异常检查服务
 * 负责检测、修复和管理订单金额异常
 */
@Provide()
export class OrderAmountAnomalyService {
  @Inject()
  logger: ILogger;

  /**
   * 调试订单数据（临时方法）
   */
  async debugOrderData(orderId: number) {
    const order = await Order.findByPk(orderId, {
      include: [
        {
          model: OrderDetail,
          include: [
            {
              model: Service,
              attributes: ['id', 'serviceName', 'basePrice'],
              required: false,
            },
          ],
        },
      ],
    });

    if (!order) {
      throw new Error(`订单 ${orderId} 不存在`);
    }

    // 计算价格
    const calculatedOriginalPrice = await this.calculateTotalOrderOriginalPrice(order);

    return {
      orderInfo: {
        id: order.id,
        sn: order.sn,
        originalPrice: order.originalPrice,
        totalFee: order.totalFee,
        cardDeduction: order.cardDeduction,
        couponDeduction: order.couponDeduction,
        status: order.status,
      },
      calculatedOriginalPrice,
      orderDetails: order.orderDetails?.map(detail => ({
        id: detail.id,
        serviceName: detail.serviceName,
        servicePrice: detail.servicePrice,
        serviceInfo: detail.service ? {
          id: detail.service.id,
          serviceName: detail.service.serviceName,
          basePrice: detail.service.basePrice,
        } : null,
      })),
      calculation: {
        expectedTotalFee: order.originalPrice - (order.cardDeduction || 0) - (order.couponDeduction || 0),
        actualTotalFee: order.totalFee,
        priceDifference: Math.abs(order.originalPrice - calculatedOriginalPrice),
      }
    };
  }

  /**
   * 生成订单金额修复建议
   * 不直接修改数据，而是提供多种修复方案供用户选择
   */
  async generateFixSuggestions(
    options: {
      clearExistingRecords?: boolean;
      orderId?: number;
    } = {}
  ) {
    try {
      const { clearExistingRecords = false, orderId } = options;

      console.log('=== 开始生成订单金额修复建议 ===');

      // 检查异常
      const checkResult = await this.checkOrderAmountAnomalies({
        threshold: 0, // 检测任何不一致
        skipExisting: false,
        clearExistingRecords,
        orderId,
      });

      if (checkResult.anomalyCount === 0) {
        console.log('✅ 未发现异常订单');
        return {
          anomalyCount: 0,
          suggestions: [],
        };
      }

      // 为每个异常生成修复建议
      const suggestions = [];
      for (const anomaly of checkResult.anomalies) {
        const suggestion = this.generateDetailedFixSuggestion(anomaly);
        suggestions.push(suggestion);
      }

      console.log(
        `发现 ${checkResult.anomalyCount} 个异常订单，已生成修复建议`
      );

      return {
        anomalyCount: checkResult.anomalyCount,
        suggestions,
      };
    } catch (error) {
      this.logger.error('生成修复建议失败:', error);
      throw error;
    }
  }

  /**
   * 检查订单金额异常
   * @param options 检查选项
   */
  async checkOrderAmountAnomalies(
    options: {
      threshold?: number; // 异常阈值百分比，默认1%，设为0则检测任何不一致
      absoluteThreshold?: number; // 绝对金额阈值（元），优先级高于百分比阈值
      limit?: number; // 返回记录数限制
      orderId?: number; // 指定订单ID
      skipExisting?: boolean; // 是否跳过已记录的异常
      clearExistingRecords?: boolean; // 是否在检测前清除所有异常记录
    } = {}
  ) {
    try {
      const {
        threshold = 1,
        absoluteThreshold,
        limit = 100,
        orderId,
        skipExisting = true,
        clearExistingRecords = false,
      } = options;

      console.log(
        '开始检查订单金额异常, clearExistingRecords: ',
        clearExistingRecords
      );
      // 如果需要清除现有记录
      if (clearExistingRecords) {
        const deletedCount = await OrderAmountAnomalyRecord.destroy({
          where: {},
        });
        console.log(`已删除 ${deletedCount} 条异常记录`);
        this.logger.info(`清除了 ${deletedCount} 条现有异常记录`);
      }

      // 构建查询条件 - 修复：包含所有收入统计相关的订单状态
      const whereConditions: any = {
        [Op.and]: [
          // 修复：不再要求 originalPrice > 0，因为缺失原价也是需要检查的异常
          { totalFee: { [Op.gte]: 0 } },
          // 修复：使用与收入统计一致的状态范围
          {
            status: {
              [Op.in]: [
                OrderStatus.已完成,
                OrderStatus.已评价,
                OrderStatus.退款中,
                OrderStatus.已退款,
              ],
            },
          },
        ],
      };

      if (orderId) {
        whereConditions.id = orderId;
      }

      // 查找可能存在金额异常的订单
      const orders = await Order.findAll({
        where: whereConditions,
        include: [
          {
            model: OrderDetail,
            include: [
              {
                model: Service,
                attributes: ['id', 'serviceName', 'basePrice'],
                required: false, // 保持false，因为服务可能被删除
              },
            ],
          },
        ],
        limit: orderId ? 1 : limit,
        order: [['createdAt', 'DESC']],
      });



      const anomalies: any[] = [];
      const existingAnomalyOrderIds = new Set<number>();

      // 如果需要跳过已存在的异常记录，先查询已记录的订单ID
      if (skipExisting) {
        const existingRecords = await OrderAmountAnomalyRecord.findAll({
          where: {
            processStatus: {
              [Op.notIn]: [
                AnomalyProcessStatus.AUTO_FIXED,
                AnomalyProcessStatus.MANUAL_FIXED,
              ],
            },
          },
          attributes: ['orderId'],
        });
        existingRecords.forEach(record =>
          existingAnomalyOrderIds.add(record.orderId)
        );
      }

      for (const order of orders) {
        // 跳过已记录的异常
        if (skipExisting && existingAnomalyOrderIds.has(order.id)) {
          continue;
        }

        const anomaly = await this.analyzeOrderAmountAnomaly(
          order,
          threshold,
          absoluteThreshold
        );
        if (anomaly) {
          anomalies.push(anomaly);
        }
      }

      this.logger.info(`订单金额异常检查完成，发现${anomalies.length}个异常`);

      return {
        anomalyCount: anomalies.length,
        threshold,
        anomalies,
      };
    } catch (error) {
      this.logger.error('检查订单金额异常失败:', error);
      throw error;
    }
  }

  /**
   * 分析单个订单的金额异常
   * @param order 订单对象
   * @param threshold 异常阈值百分比
   * @param absoluteThreshold 绝对金额阈值
   */
  private async analyzeOrderAmountAnomaly(
    order: Order,
    threshold: number,
    absoluteThreshold?: number
  ) {
    try {
      // 计算订单应有的原价（包含主订单和追加服务）
      const calculatedOriginalPrice =
        await this.calculateTotalOrderOriginalPrice(order);

      // 使用实际原价或计算原价来计算预期实付金额
      const actualOriginalPrice =
        order.originalPrice || calculatedOriginalPrice;
      const expectedTotalFee =
        actualOriginalPrice -
        (order.cardDeduction || 0) -
        (order.couponDeduction || 0);

      // 检查各种异常情况
      const anomalies: any[] = [];

      // 1. 检查原价是否缺失
      if (!order.originalPrice || order.originalPrice <= 0) {
        anomalies.push({
          type: OrderAmountAnomalyType.MISSING_ORIGINAL_PRICE,
          description: '订单原价缺失或为0',
          severity: 3,
          canAutoFix: calculatedOriginalPrice > 0,
          calculatedOriginalPrice,
          anomalyAmount: Math.abs(
            calculatedOriginalPrice - (order.originalPrice || 0)
          ),
        });
      }

      // 2. 检查原价与计算价格的差异
      if (order.originalPrice > 0 && calculatedOriginalPrice > 0) {
        const priceDifference = Math.abs(
          order.originalPrice - calculatedOriginalPrice
        );
        const differenceRate =
          (priceDifference / calculatedOriginalPrice) * 100;

        // 判断是否超过阈值
        let isAnomalous = false;
        let description = '';

        if (absoluteThreshold !== undefined) {
          // 使用绝对金额阈值
          isAnomalous = priceDifference > absoluteThreshold;
          description = `订单原价与计算价格不匹配，差异${priceDifference.toFixed(
            2
          )}元`;
        } else if (threshold === 0) {
          // 阈值为0时，检测任何不一致
          isAnomalous = priceDifference > 0;
          description = `订单原价与计算价格不匹配，差异${priceDifference.toFixed(
            2
          )}元（${differenceRate.toFixed(2)}%）`;
        } else {
          // 使用百分比阈值
          isAnomalous = differenceRate > threshold;
          description = `订单原价与计算价格不匹配，差异${differenceRate.toFixed(
            2
          )}%`;
        }

        if (isAnomalous) {
          anomalies.push({
            type: OrderAmountAnomalyType.PRICE_MISMATCH,
            description,
            severity: this.calculateSeverity(differenceRate),
            canAutoFix: this.canAutoFixPriceMismatch(
              order,
              calculatedOriginalPrice
            ),
            calculatedOriginalPrice,
            anomalyAmount: priceDifference,
          });
        }
      }

      // 3. 检查实付金额异常 - 修复：使用实际原价进行检查
      if (actualOriginalPrice > 0) {
        const totalFeeDifference = Math.abs(order.totalFee - expectedTotalFee);
        const totalFeeRate =
          actualOriginalPrice > 0
            ? (totalFeeDifference / actualOriginalPrice) * 100
            : 0;

        // 判断实付金额是否异常
        let isTotalFeeAnomalous = false;
        let totalFeeDescription = '';

        if (absoluteThreshold !== undefined) {
          // 使用绝对金额阈值
          isTotalFeeAnomalous = totalFeeDifference > absoluteThreshold;
          totalFeeDescription = `实付金额计算异常，差异${totalFeeDifference.toFixed(
            2
          )}元`;
        } else if (threshold === 0) {
          // 阈值为0时，检测任何不一致
          isTotalFeeAnomalous = totalFeeDifference > 0;
          totalFeeDescription = `实付金额计算异常，差异${totalFeeDifference.toFixed(
            2
          )}元（${totalFeeRate.toFixed(2)}%）`;
        } else {
          // 使用百分比阈值
          isTotalFeeAnomalous = totalFeeRate > threshold;
          totalFeeDescription = `实付金额计算异常，差异${totalFeeRate.toFixed(
            2
          )}%`;
        }

        if (isTotalFeeAnomalous) {
          anomalies.push({
            type: OrderAmountAnomalyType.CALCULATION_ERROR,
            description: totalFeeDescription,
            severity: this.calculateSeverity(totalFeeRate),
            canAutoFix: this.canAutoFixCalculationError(order),
            calculatedOriginalPrice,
            anomalyAmount: totalFeeDifference,
          });
        }
      }

      // 4. 检查优惠金额异常 - 修复：使用实际原价进行检查
      const totalDiscount =
        (order.cardDeduction || 0) + (order.couponDeduction || 0);
      if (actualOriginalPrice > 0 && totalDiscount > actualOriginalPrice) {
        anomalies.push({
          type: OrderAmountAnomalyType.DISCOUNT_ANOMALY,
          description: `优惠金额(${totalDiscount})超过订单原价(${actualOriginalPrice})`,
          severity: 4,
          canAutoFix: false,
          calculatedOriginalPrice,
          anomalyAmount: totalDiscount - actualOriginalPrice,
        });
      }

      // 如果发现异常，返回最严重的一个
      if (anomalies.length > 0) {
        const mostSevereAnomaly = anomalies.reduce((prev, current) =>
          current.severity > prev.severity ? current : prev
        );

        return {
          orderId: order.id,
          orderSn: order.sn,
          anomalyType: mostSevereAnomaly.type,
          description: mostSevereAnomaly.description,
          severity: mostSevereAnomaly.severity,
          canAutoFix: mostSevereAnomaly.canAutoFix,
          calculatedOriginalPrice: mostSevereAnomaly.calculatedOriginalPrice,
          anomalyAmount: mostSevereAnomaly.anomalyAmount,
          currentData: {
            originalPrice: order.originalPrice,
            totalFee: order.totalFee,
            cardDeduction: order.cardDeduction,
            couponDeduction: order.couponDeduction,
          },
          allAnomalies: anomalies,
        };
      }

      return null;
    } catch (error) {
      this.logger.error(`分析订单${order.sn}金额异常失败:`, error);
      return null;
    }
  }

  /**
   * 计算订单总原价（包含主订单和追加服务）
   * @param order 订单对象
   */
  private async calculateTotalOrderOriginalPrice(
    order: Order
  ): Promise<number> {
    let totalPrice = 0;

    // 1. 计算主订单明细价格
    const mainOrderPrice = this.calculateOrderOriginalPrice(
      order.orderDetails || []
    );
    totalPrice += mainOrderPrice;

    // 2. 计算追加服务价格
    // 通过订单明细ID查找追加服务
    const orderDetailIds = (order.orderDetails || []).map(detail => detail.id);

    if (orderDetailIds.length > 0) {
      const additionalServices = await AdditionalServiceOrder.findAll({
        where: { orderDetailId: { [Op.in]: orderDetailIds } },
        attributes: ['id', 'originalPrice'], // 包含ID字段用于调试
      });

      for (const additionalService of additionalServices) {
        if (
          additionalService.originalPrice &&
          additionalService.originalPrice > 0
        ) {
          totalPrice += Number(additionalService.originalPrice);
        }
      }
    }

    return totalPrice;
  }

  /**
   * 计算订单明细原价
   * @param orderDetails 订单详情列表
   */
  private calculateOrderOriginalPrice(orderDetails: OrderDetail[]): number {
    let totalPrice = 0;

    for (const detail of orderDetails) {
      let priceToAdd = 0;

      // 优先使用订单明细中保存的服务价格
      if (detail.servicePrice && Number(detail.servicePrice) > 0) {
        priceToAdd = Number(detail.servicePrice);
      }
      // 如果明细价格为空，尝试使用关联服务的基础价格
      else if (detail.service && detail.service.basePrice && Number(detail.service.basePrice) > 0) {
        priceToAdd = Number(detail.service.basePrice);
      }
      // 如果都没有，记录警告但不影响计算
      else {
        this.logger.warn(`订单明细 ${detail.id} (${detail.serviceName}) 没有有效的价格信息`);
        continue;
      }

      if (!isNaN(priceToAdd) && isFinite(priceToAdd) && priceToAdd > 0) {
        totalPrice += priceToAdd;
      } else {
        this.logger.warn(`价格数据异常，跳过明细 ${detail.id}: ${priceToAdd}`);
      }
    }
    return totalPrice;
  }

  /**
   * 计算异常严重程度
   * @param differenceRate 差异百分比
   */
  private calculateSeverity(differenceRate: number): number {
    if (differenceRate >= 50) return 5;
    if (differenceRate >= 20) return 4;
    if (differenceRate >= 10) return 3;
    if (differenceRate >= 5) return 2;
    return 1;
  }

  /**
   * 判断价格不匹配是否可以自动修复
   */
  private canAutoFixPriceMismatch(
    order: Order,
    calculatedPrice: number
  ): boolean {
    // 如果计算出的价格合理且订单状态允许修改，则可以自动修复
    return (
      calculatedPrice > 0 &&
      ['待付款', '已付款', '已确认'].includes(order.status)
    );
  }

  /**
   * 判断计算错误是否可以自动修复
   */
  private canAutoFixCalculationError(order: Order): boolean {
    // 如果订单状态允许修改，则可以自动修复
    return ['待付款', '已付款', '已确认'].includes(order.status);
  }

  /**
   * 创建异常记录
   * @param anomalyData 异常数据
   */
  async createAnomalyRecord(anomalyData: any) {
    try {
      // 验证订单状态，不为已取消或待付款的订单创建异常记录
      const order = await Order.findByPk(anomalyData.orderId, {
        attributes: ['id', 'status'],
      });

      if (!order) {
        throw new Error(`订单不存在: ${anomalyData.orderId}`);
      }

      if (
        [OrderStatus.待付款, OrderStatus.已取消].includes(
          order.status as OrderStatus
        )
      ) {
        this.logger.warn(
          `跳过为无效状态订单创建异常记录: ${anomalyData.orderSn}, 状态: ${order.status}`
        );
        return null;
      }

      const record = await OrderAmountAnomalyRecord.create({
        orderId: anomalyData.orderId,
        orderSn: anomalyData.orderSn,
        anomalyType: anomalyData.anomalyType,
        processStatus: AnomalyProcessStatus.PENDING,
        description: anomalyData.description,
        anomalyDetails: JSON.stringify(anomalyData),
        originalPrice: anomalyData.currentData.originalPrice || 0,
        totalFee: anomalyData.currentData.totalFee || 0,
        cardDeduction: anomalyData.currentData.cardDeduction || 0,
        couponDeduction: anomalyData.currentData.couponDeduction || 0,
        calculatedOriginalPrice: anomalyData.calculatedOriginalPrice,
        anomalyAmount: anomalyData.anomalyAmount,
        severity: anomalyData.severity,
        canAutoFix: anomalyData.canAutoFix,
        autoFixAttempts: 0,
        fixSuggestion: this.generateFixSuggestion(anomalyData),
        isReverted: false,
      });

      this.logger.info(`创建异常记录成功，记录ID: ${record.id}`);
      return record;
    } catch (error) {
      this.logger.error('创建异常记录失败:', error);
      throw error;
    }
  }

  /**
   * 生成修复建议
   */
  private generateFixSuggestion(anomalyData: any): string {
    const current = anomalyData.currentData;
    const calculated = anomalyData.calculatedOriginalPrice;
    const difference = anomalyData.anomalyAmount;

    switch (anomalyData.anomalyType) {
      case OrderAmountAnomalyType.MISSING_ORIGINAL_PRICE: {
        return `【原价缺失】当前原价: ${
          current.originalPrice || 0
        }元，根据服务项目计算应为: ${calculated}元。建议修复：将原价设置为 ${calculated}元`;
      }

      case OrderAmountAnomalyType.PRICE_MISMATCH: {
        const higherOrLower =
          current.originalPrice > calculated ? '高于' : '低于';
        return `【原价不匹配】当前原价: ${current.originalPrice}元，${higherOrLower}计算值 ${difference}元。计算依据：服务项目总价 ${calculated}元。建议修复：将原价调整为 ${calculated}元`;
      }

      case OrderAmountAnomalyType.CALCULATION_ERROR: {
        const expectedTotalFee =
          calculated -
          (current.cardDeduction || 0) -
          (current.couponDeduction || 0);
        const totalFeeHigherOrLower =
          current.totalFee > expectedTotalFee ? '高于' : '低于';
        return `【实付金额异常】当前实付: ${
          current.totalFee
        }元，${totalFeeHigherOrLower}预期值 ${difference}元。计算公式：原价(${
          current.originalPrice
        }) - 权益卡抵扣(${current.cardDeduction || 0}) - 代金券抵扣(${
          current.couponDeduction || 0
        }) = ${expectedTotalFee}元。建议修复：将实付金额调整为 ${expectedTotalFee}元`;
      }

      case OrderAmountAnomalyType.DISCOUNT_ANOMALY: {
        const totalDiscount =
          (current.cardDeduction || 0) + (current.couponDeduction || 0);
        return `【优惠金额异常】总优惠金额 ${totalDiscount}元 超过原价 ${
          current.originalPrice
        }元。详情：权益卡抵扣 ${current.cardDeduction || 0}元 + 代金券抵扣 ${
          current.couponDeduction || 0
        }元 = ${totalDiscount}元。建议检查：1.权益卡使用规则 2.代金券使用条件 3.是否存在重复抵扣`;
      }

      default: {
        return `【未知异常】订单金额存在异常，差异金额: ${difference}元。建议人工检查订单详情并核实各项金额的准确性`;
      }
    }
  }

  /**
   * 批量检查并创建异常记录
   * @param options 检查选项
   */
  async batchCheckAndCreateAnomalies(
    options: {
      threshold?: number;
      absoluteThreshold?: number;
      limit?: number;
      autoCreateRecords?: boolean;
      clearExistingRecords?: boolean;
    } = {}
  ) {
    try {
      const { autoCreateRecords = true } = options;

      console.log('开始批量检查并创建异常记录, options: ', options);

      // 检查异常
      const checkResult = await this.checkOrderAmountAnomalies(options);

      if (!autoCreateRecords || checkResult.anomalies.length === 0) {
        return checkResult;
      }

      // 创建异常记录
      const createdRecords: OrderAmountAnomalyRecord[] = [];
      for (const anomaly of checkResult.anomalies) {
        try {
          const record = await this.createAnomalyRecord(anomaly);
          if (record) {
            createdRecords.push(record);
          }
        } catch (error) {
          this.logger.error(`创建异常记录失败，订单${anomaly.orderSn}:`, error);
        }
      }

      return {
        ...checkResult,
        createdRecords: createdRecords.length,
        records: createdRecords,
      };
    } catch (error) {
      this.logger.error('批量检查并创建异常记录失败:', error);
      throw error;
    }
  }

  /**
   * 清理无效的异常记录（已取消、待付款订单的记录）
   */
  async cleanupInvalidAnomalyRecords() {
    try {
      // 查找关联到无效订单的异常记录
      const invalidRecords = await OrderAmountAnomalyRecord.findAll({
        include: [
          {
            model: Order,
            where: {
              status: { [Op.in]: [OrderStatus.待付款, OrderStatus.已取消] },
            },
            attributes: ['id', 'sn', 'status'],
          },
        ],
      });

      if (invalidRecords.length === 0) {
        return {
          message: '没有找到需要清理的无效记录',
          cleanedCount: 0,
        };
      }

      // 删除这些记录
      const recordIds = invalidRecords.map(record => record.id);
      const deletedCount = await OrderAmountAnomalyRecord.destroy({
        where: {
          id: { [Op.in]: recordIds },
        },
      });

      this.logger.info(`清理了 ${deletedCount} 条无效的异常记录`);

      return {
        message: `成功清理了 ${deletedCount} 条无效的异常记录`,
        cleanedCount: deletedCount,
        cleanedRecords: invalidRecords.map(record => ({
          id: record.id,
          orderSn: record.order?.sn,
          orderStatus: record.order?.status,
        })),
      };
    } catch (error) {
      this.logger.error('清理无效异常记录失败:', error);
      throw error;
    }
  }

  /**
   * 更新现有异常记录的修复建议
   */
  async updateFixSuggestions() {
    try {
      const records = await OrderAmountAnomalyRecord.findAll({
        where: {
          processStatus: {
            [Op.in]: [
              AnomalyProcessStatus.PENDING,
              AnomalyProcessStatus.AUTO_FIX_FAILED,
              AnomalyProcessStatus.MANUAL_REQUIRED,
            ],
          },
        },
      });

      let updatedCount = 0;
      for (const record of records) {
        try {
          // 重新解析异常数据
          const anomalyData = JSON.parse(record.anomalyDetails);

          // 生成新的修复建议
          const newFixSuggestion = this.generateFixSuggestion(anomalyData);

          // 更新记录
          await record.update({
            fixSuggestion: newFixSuggestion,
          });

          updatedCount++;
        } catch (error) {
          this.logger.error(`更新记录 ${record.id} 的修复建议失败:`, error);
        }
      }

      return {
        message: `成功更新了 ${updatedCount} 条记录的修复建议`,
        updatedCount,
        totalRecords: records.length,
      };
    } catch (error) {
      this.logger.error('更新修复建议失败:', error);
      throw error;
    }
  }

  /**
   * 获取异常记录列表
   * @param options 查询选项
   */
  async getAnomalyRecords(
    options: {
      status?: AnomalyProcessStatus[];
      anomalyType?: OrderAmountAnomalyType[];
      severity?: number[];
      canAutoFix?: boolean;
      limit?: number;
      offset?: number;
    } = {}
  ) {
    try {
      const {
        status,
        anomalyType,
        severity,
        canAutoFix,
        limit = 50,
        offset = 0,
      } = options;

      const whereConditions: any = {};

      if (status && status.length > 0) {
        whereConditions.processStatus = { [Op.in]: status };
      }

      if (anomalyType && anomalyType.length > 0) {
        whereConditions.anomalyType = { [Op.in]: anomalyType };
      }

      if (severity && severity.length > 0) {
        whereConditions.severity = { [Op.in]: severity };
      }

      if (canAutoFix !== undefined) {
        whereConditions.canAutoFix = canAutoFix;
      }

      const { rows: records, count: total } =
        await OrderAmountAnomalyRecord.findAndCountAll({
          where: whereConditions,
          include: [
            {
              model: Order,
              attributes: ['id', 'sn', 'status', 'orderTime'],
              where: {
                // 只显示有效订单的异常记录
                status: {
                  [Op.notIn]: [OrderStatus.待付款, OrderStatus.已取消],
                },
              },
            },
          ],
          limit,
          offset,
          order: [['createdAt', 'DESC']],
        });

      return {
        records,
        total,
        limit,
        offset,
      };
    } catch (error) {
      this.logger.error('获取异常记录列表失败:', error);
      throw error;
    }
  }

  /**
   * 自动修复异常
   * @param recordId 异常记录ID
   */
  async autoFixAnomaly(recordId: number) {
    const transaction = await OrderAmountAnomalyRecord.sequelize!.transaction();

    try {
      // 获取异常记录
      const record = await OrderAmountAnomalyRecord.findByPk(recordId, {
        include: [{ model: Order, include: [OrderDetail] }],
        transaction,
      });

      if (!record) {
        throw new Error('异常记录不存在');
      }

      if (!record.canAutoFix) {
        throw new Error('该异常不支持自动修复');
      }

      // 更新记录状态为修复中
      await record.update(
        {
          processStatus: AnomalyProcessStatus.AUTO_FIXING,
          autoFixAttempts: record.autoFixAttempts + 1,
          lastFixAttemptAt: new Date(),
        },
        { transaction }
      );

      const startTime = Date.now();
      let fixResult: FixResult = FixResult.FAILED;
      let errorMessage: string | undefined;
      let beforeData: any;
      let afterData: any;

      try {
        // 执行修复操作
        const fixSuccess = await this.executeAutoFix(record, transaction);

        if (fixSuccess) {
          fixResult = FixResult.SUCCESS;
          await record.update(
            {
              processStatus: AnomalyProcessStatus.AUTO_FIXED,
              handledAt: new Date(),
            },
            { transaction }
          );
        } else {
          fixResult = FixResult.FAILED;
          await record.update(
            {
              processStatus: AnomalyProcessStatus.AUTO_FIX_FAILED,
            },
            { transaction }
          );
        }
      } catch (error) {
        fixResult = FixResult.FAILED;
        errorMessage = error.message;
        await record.update(
          {
            processStatus: AnomalyProcessStatus.AUTO_FIX_FAILED,
          },
          { transaction }
        );
      }

      // 创建修复日志
      await OrderAmountFixLog.create(
        {
          anomalyRecordId: record.id,
          orderId: record.orderId,
          orderSn: record.orderSn,
          operationType: FixOperationType.AUTO_FIX,
          result: fixResult,
          beforeData: JSON.stringify(beforeData || {}),
          afterData: JSON.stringify(afterData || {}),
          description: `自动修复${record.anomalyType}异常`,
          executionTime: Date.now() - startTime,
          errorMessage,
          operatorType: 'system',
          canRevert: fixResult === FixResult.SUCCESS,
          revertData:
            fixResult === FixResult.SUCCESS
              ? JSON.stringify(beforeData)
              : undefined,
        },
        { transaction }
      );

      await transaction.commit();

      this.logger.info(`自动修复异常记录${recordId}完成，结果: ${fixResult}`);

      return {
        success: fixResult === FixResult.SUCCESS,
        result: fixResult,
        message:
          fixResult === FixResult.SUCCESS
            ? '自动修复成功'
            : `自动修复失败: ${errorMessage}`,
      };
    } catch (error) {
      await transaction.rollback();
      this.logger.error(`自动修复异常记录${recordId}失败:`, error);
      throw error;
    }
  }

  /**
   * 执行自动修复
   * @param record 异常记录
   * @param transaction 事务
   */
  private async executeAutoFix(
    record: OrderAmountAnomalyRecord,
    transaction: Transaction
  ): Promise<boolean> {
    const order = record.order;
    if (!order) {
      throw new Error('订单信息不存在');
    }

    switch (record.anomalyType) {
      case OrderAmountAnomalyType.MISSING_ORIGINAL_PRICE:
        if (
          record.calculatedOriginalPrice &&
          record.calculatedOriginalPrice > 0
        ) {
          await order.update(
            {
              originalPrice: record.calculatedOriginalPrice,
            },
            { transaction }
          );
          return true;
        }
        break;

      case OrderAmountAnomalyType.PRICE_MISMATCH:
        if (
          record.calculatedOriginalPrice &&
          record.calculatedOriginalPrice > 0
        ) {
          await order.update(
            {
              originalPrice: record.calculatedOriginalPrice,
            },
            { transaction }
          );
          return true;
        }
        break;

      case OrderAmountAnomalyType.CALCULATION_ERROR:
        {
          // 重新计算实付金额
          const newTotalFee =
            (order.originalPrice || 0) -
            (order.cardDeduction || 0) -
            (order.couponDeduction || 0);
          if (newTotalFee >= 0) {
            await order.update(
              {
                totalFee: newTotalFee,
              },
              { transaction }
            );
            return true;
          }
        }
        break;
    }

    return false;
  }

  /**
   * 批量自动修复异常
   * @param options 修复选项
   */
  async batchAutoFixAnomalies(
    options: {
      recordIds?: number[];
      maxAttempts?: number;
      onlyAutoFixable?: boolean;
    } = {}
  ) {
    try {
      const { recordIds, maxAttempts = 3, onlyAutoFixable = true } = options;

      // 构建查询条件
      const whereConditions: any = {
        processStatus: AnomalyProcessStatus.PENDING,
        autoFixAttempts: { [Op.lt]: maxAttempts },
      };

      if (onlyAutoFixable) {
        whereConditions.canAutoFix = true;
      }

      if (recordIds && recordIds.length > 0) {
        whereConditions.id = { [Op.in]: recordIds };
      }

      // 获取待修复的记录
      const records = await OrderAmountAnomalyRecord.findAll({
        where: whereConditions,
        limit: 50, // 限制批量处理数量
      });

      const results = {
        total: records.length,
        success: 0,
        failed: 0,
        details: [] as any[],
      };

      // 逐个修复
      for (const record of records) {
        try {
          const fixResult = await this.autoFixAnomaly(record.id);
          if (fixResult.success) {
            results.success++;
          } else {
            results.failed++;
          }
          results.details.push({
            recordId: record.id,
            orderSn: record.orderSn,
            success: fixResult.success,
            message: fixResult.message,
          });
        } catch (error) {
          results.failed++;
          results.details.push({
            recordId: record.id,
            orderSn: record.orderSn,
            success: false,
            message: error.message,
          });
        }
      }

      this.logger.info(
        `批量自动修复完成，成功${results.success}个，失败${results.failed}个`
      );
      return results;
    } catch (error) {
      this.logger.error('批量自动修复异常失败:', error);
      throw error;
    }
  }

  /**
   * 手动修复异常
   * @param recordId 异常记录ID
   * @param fixData 修复数据
   * @param operatorInfo 操作人信息
   */
  async manualFixAnomaly(
    recordId: number,
    fixData: {
      originalPrice?: number;
      totalFee?: number;
      cardDeduction?: number;
      couponDeduction?: number;
    },
    operatorInfo: {
      operatorId: number;
      operatorName: string;
      remark?: string;
    }
  ) {
    const transaction = await OrderAmountAnomalyRecord.sequelize!.transaction();

    try {
      // 获取异常记录
      const record = await OrderAmountAnomalyRecord.findByPk(recordId, {
        include: [{ model: Order }],
        transaction,
      });

      if (!record) {
        throw new Error('异常记录不存在');
      }

      if (record.processStatus === AnomalyProcessStatus.MANUAL_FIXED) {
        throw new Error('该异常已经被手动修复');
      }

      const order = record.order;
      if (!order) {
        throw new Error('订单信息不存在');
      }

      // 记录修复前的数据
      const beforeData = {
        originalPrice: order.originalPrice,
        totalFee: order.totalFee,
        cardDeduction: order.cardDeduction,
        couponDeduction: order.couponDeduction,
      };

      // 更新记录状态
      await record.update(
        {
          processStatus: AnomalyProcessStatus.MANUAL_PROCESSING,
          handlerId: operatorInfo.operatorId,
          handlerName: operatorInfo.operatorName,
          handlerRemark: operatorInfo.remark,
        },
        { transaction }
      );

      const startTime = Date.now();
      let fixResult: FixResult = FixResult.SUCCESS;
      let errorMessage: string | undefined;

      try {
        // 执行手动修复
        const updateData: any = {};
        if (fixData.originalPrice !== undefined)
          updateData.originalPrice = fixData.originalPrice;
        if (fixData.totalFee !== undefined)
          updateData.totalFee = fixData.totalFee;
        if (fixData.cardDeduction !== undefined)
          updateData.cardDeduction = fixData.cardDeduction;
        if (fixData.couponDeduction !== undefined)
          updateData.couponDeduction = fixData.couponDeduction;

        if (Object.keys(updateData).length > 0) {
          await order.update(updateData, { transaction });
        }

        // 更新记录状态为已修复
        await record.update(
          {
            processStatus: AnomalyProcessStatus.MANUAL_FIXED,
            handledAt: new Date(),
          },
          { transaction }
        );
      } catch (error) {
        fixResult = FixResult.FAILED;
        errorMessage = error.message;
        await record.update(
          {
            processStatus: AnomalyProcessStatus.PENDING,
          },
          { transaction }
        );
      }

      // 记录修复后的数据
      const afterData = {
        originalPrice: fixData.originalPrice ?? order.originalPrice,
        totalFee: fixData.totalFee ?? order.totalFee,
        cardDeduction: fixData.cardDeduction ?? order.cardDeduction,
        couponDeduction: fixData.couponDeduction ?? order.couponDeduction,
      };

      // 创建修复日志
      await OrderAmountFixLog.create(
        {
          anomalyRecordId: record.id,
          orderId: record.orderId,
          orderSn: record.orderSn,
          operationType: FixOperationType.MANUAL_FIX,
          result: fixResult,
          beforeData: JSON.stringify(beforeData),
          afterData: JSON.stringify(afterData),
          description: `手动修复${record.anomalyType}异常`,
          executionTime: Date.now() - startTime,
          errorMessage,
          operatorId: operatorInfo.operatorId,
          operatorName: operatorInfo.operatorName,
          operatorType: 'admin',
          canRevert: fixResult === FixResult.SUCCESS,
          revertData:
            fixResult === FixResult.SUCCESS
              ? JSON.stringify(beforeData)
              : undefined,
          remark: operatorInfo.remark,
        },
        { transaction }
      );

      await transaction.commit();

      this.logger.info(`手动修复异常记录${recordId}完成，结果: ${fixResult}`);

      return {
        success: fixResult === FixResult.SUCCESS,
        result: fixResult,
        message:
          fixResult === FixResult.SUCCESS
            ? '手动修复成功'
            : `手动修复失败: ${errorMessage}`,
      };
    } catch (error) {
      await transaction.rollback();
      this.logger.error(`手动修复异常记录${recordId}失败:`, error);
      throw error;
    }
  }

  /**
   * 回退修复操作
   * @param logId 修复日志ID
   * @param operatorInfo 操作人信息
   */
  async revertFix(
    logId: number,
    operatorInfo: {
      operatorId: number;
      operatorName: string;
      reason: string;
    }
  ) {
    const transaction = await OrderAmountFixLog.sequelize!.transaction();

    try {
      // 获取修复日志
      const log = await OrderAmountFixLog.findByPk(logId, {
        include: [{ model: OrderAmountAnomalyRecord }, { model: Order }],
        transaction,
      });

      if (!log) {
        throw new Error('修复日志不存在');
      }

      if (!log.canRevert) {
        throw new Error('该修复操作不支持回退');
      }

      if (log.isReverted) {
        throw new Error('该修复操作已经被回退');
      }

      if (!log.revertData) {
        throw new Error('缺少回退数据');
      }

      const order = log.order;
      const record = log.anomalyRecord;

      if (!order || !record) {
        throw new Error('关联数据不存在');
      }

      // 解析回退数据
      const revertData = JSON.parse(log.revertData);

      // 执行回退操作
      await order.update(revertData, { transaction });

      // 更新修复日志
      await log.update(
        {
          isReverted: true,
          revertedAt: new Date(),
          revertOperatorId: operatorInfo.operatorId,
          revertOperatorName: operatorInfo.operatorName,
          revertReason: operatorInfo.reason,
        },
        { transaction }
      );

      // 更新异常记录状态
      await record.update(
        {
          processStatus: AnomalyProcessStatus.PENDING,
          isReverted: true,
          revertedAt: new Date(),
          revertReason: operatorInfo.reason,
        },
        { transaction }
      );

      await transaction.commit();

      this.logger.info(`回退修复操作${logId}成功`);

      return {
        success: true,
        message: '回退操作成功',
      };
    } catch (error) {
      await transaction.rollback();
      this.logger.error(`回退修复操作${logId}失败:`, error);
      throw error;
    }
  }

  /**
   * 获取异常统计报告
   */
  async getAnomalyStatistics() {
    try {
      // 总异常数量
      const totalAnomalies = await OrderAmountAnomalyRecord.count();

      // 按状态统计
      const statusStats = await OrderAmountAnomalyRecord.findAll({
        attributes: ['processStatus', [literal('COUNT(*)'), 'count']],
        group: ['processStatus'],
        raw: true,
      });

      // 按异常类型统计
      const typeStats = await OrderAmountAnomalyRecord.findAll({
        attributes: ['anomalyType', [literal('COUNT(*)'), 'count']],
        group: ['anomalyType'],
        raw: true,
      });

      // 按严重程度统计
      const severityStats = await OrderAmountAnomalyRecord.findAll({
        attributes: ['severity', [literal('COUNT(*)'), 'count']],
        group: ['severity'],
        order: [['severity', 'ASC']],
        raw: true,
      });

      // 自动修复成功率
      const autoFixedCount = await OrderAmountAnomalyRecord.count({
        where: { processStatus: AnomalyProcessStatus.AUTO_FIXED },
      });

      const autoFixableCount = await OrderAmountAnomalyRecord.count({
        where: { canAutoFix: true },
      });

      // 最近7天的异常趋势
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

      const recentTrend = await OrderAmountAnomalyRecord.findAll({
        attributes: [
          [literal('DATE(createdAt)'), 'date'],
          [literal('COUNT(*)'), 'count'],
        ],
        where: {
          createdAt: { [Op.gte]: sevenDaysAgo },
        },
        group: ['createdAt'],
        order: [['createdAt', 'ASC']],
        raw: true,
      });

      return {
        totalAnomalies,
        statusStatistics: statusStats,
        typeStatistics: typeStats,
        severityStatistics: severityStats,
        autoFixStatistics: {
          autoFixableCount,
          autoFixedCount,
          autoFixSuccessRate:
            autoFixableCount > 0
              ? ((autoFixedCount / autoFixableCount) * 100).toFixed(2) + '%'
              : '0%',
        },
        recentTrend,
      };
    } catch (error) {
      this.logger.error('获取异常统计报告失败:', error);
      throw error;
    }
  }

  /**
   * 获取修复日志
   * @param options 查询选项
   */
  async getFixLogs(
    options: {
      anomalyRecordId?: number;
      orderId?: number;
      operationType?: FixOperationType[];
      result?: FixResult[];
      limit?: number;
      offset?: number;
    } = {}
  ) {
    try {
      const {
        anomalyRecordId,
        orderId,
        operationType,
        result,
        limit = 50,
        offset = 0,
      } = options;

      const whereConditions: any = {};

      if (anomalyRecordId) {
        whereConditions.anomalyRecordId = anomalyRecordId;
      }

      if (orderId) {
        whereConditions.orderId = orderId;
      }

      if (operationType && operationType.length > 0) {
        whereConditions.operationType = { [Op.in]: operationType };
      }

      if (result && result.length > 0) {
        whereConditions.result = { [Op.in]: result };
      }

      const { rows: logs, count: total } =
        await OrderAmountFixLog.findAndCountAll({
          where: whereConditions,
          include: [
            {
              model: OrderAmountAnomalyRecord,
              attributes: ['id', 'anomalyType', 'description'],
            },
            {
              model: Order,
              attributes: ['id', 'sn', 'status'],
            },
          ],
          limit,
          offset,
          order: [['createdAt', 'DESC']],
        });

      return {
        logs,
        total,
        limit,
        offset,
      };
    } catch (error) {
      this.logger.error('获取修复日志失败:', error);
      throw error;
    }
  }

  /**
   * 生成异常问题清单
   * @param options 生成选项
   */
  async generateAnomalyReport(
    options: {
      status?: AnomalyProcessStatus[];
      severity?: number[];
      includeDetails?: boolean;
    } = {}
  ) {
    try {
      const {
        status = [
          AnomalyProcessStatus.PENDING,
          AnomalyProcessStatus.MANUAL_REQUIRED,
        ],
        severity,
        includeDetails = true,
      } = options;

      const whereConditions: any = {
        processStatus: { [Op.in]: status },
      };

      if (severity && severity.length > 0) {
        whereConditions.severity = { [Op.in]: severity };
      }

      const records = await OrderAmountAnomalyRecord.findAll({
        where: whereConditions,
        include: [
          {
            model: Order,
            attributes: ['id', 'sn', 'status', 'orderTime', 'customerId'],
          },
        ],
        order: [
          ['severity', 'DESC'],
          ['createdAt', 'DESC'],
        ],
      });

      const report = {
        generateTime: new Date(),
        totalCount: records.length,
        summary: {
          bySeverity: {} as any,
          byType: {} as any,
          byStatus: {} as any,
        },
        issues: records.map(record => ({
          id: record.id,
          orderId: record.orderId,
          orderSn: record.orderSn,
          anomalyType: record.anomalyType,
          description: record.description,
          severity: record.severity,
          processStatus: record.processStatus,
          anomalyAmount: record.anomalyAmount,
          canAutoFix: record.canAutoFix,
          fixSuggestion: record.fixSuggestion,
          createdAt: record.createdAt,
          orderInfo: record.order
            ? {
                status: record.order.status,
                orderTime: record.order.orderTime,
                customerId: record.order.customerId,
              }
            : null,
          details: includeDetails
            ? JSON.parse(record.anomalyDetails)
            : undefined,
        })),
      };

      // 生成汇总统计
      records.forEach(record => {
        // 按严重程度统计
        const severityKey = `severity_${record.severity}`;
        report.summary.bySeverity[severityKey] =
          (report.summary.bySeverity[severityKey] || 0) + 1;

        // 按类型统计
        report.summary.byType[record.anomalyType] =
          (report.summary.byType[record.anomalyType] || 0) + 1;

        // 按状态统计
        report.summary.byStatus[record.processStatus] =
          (report.summary.byStatus[record.processStatus] || 0) + 1;
      });

      this.logger.info(`生成异常问题清单完成，共${records.length}个问题`);
      return report;
    } catch (error) {
      this.logger.error('生成异常问题清单失败:', error);
      throw error;
    }
  }

  /**
   * 忽略异常记录
   * @param recordId 异常记录ID
   * @param operatorInfo 操作人信息
   */
  async ignoreAnomaly(
    recordId: number,
    operatorInfo: {
      operatorId: number;
      operatorName: string;
      reason: string;
    }
  ) {
    try {
      const record = await OrderAmountAnomalyRecord.findByPk(recordId);

      if (!record) {
        throw new Error('异常记录不存在');
      }

      if (record.processStatus === AnomalyProcessStatus.IGNORED) {
        throw new Error('该异常已经被忽略');
      }

      await record.update({
        processStatus: AnomalyProcessStatus.IGNORED,
        handlerId: operatorInfo.operatorId,
        handlerName: operatorInfo.operatorName,
        handledAt: new Date(),
        handlerRemark: `忽略原因: ${operatorInfo.reason}`,
      });

      this.logger.info(`忽略异常记录${recordId}成功`);

      return {
        success: true,
        message: '异常记录已忽略',
      };
    } catch (error) {
      this.logger.error(`忽略异常记录${recordId}失败:`, error);
      throw error;
    }
  }

  /**
   * 生成详细的修复建议
   * 提供多种修复方案供用户选择，而不是直接修改数据
   */
  private generateDetailedFixSuggestion(anomaly: any) {
    const suggestions = [];
    const currentData = anomaly.currentData;
    const calculatedOriginalPrice = anomaly.calculatedOriginalPrice || 0;

    // 计算期望的实付金额
    const expectedTotalFee =
      calculatedOriginalPrice -
      (currentData.cardDeduction || 0) -
      (currentData.couponDeduction || 0);

    switch (anomaly.anomalyType) {
      case OrderAmountAnomalyType.MISSING_ORIGINAL_PRICE:
        suggestions.push({
          type: 'fix_original_price',
          title: '设置原价',
          description: `将原价设置为根据订单明细计算的价格: ${calculatedOriginalPrice}元`,
          action: {
            field: 'originalPrice',
            value: calculatedOriginalPrice,
            reason: '根据订单明细计算得出',
          },
          risk: 'low',
          recommended: true,
        });
        break;

      case OrderAmountAnomalyType.PRICE_MISMATCH:
        suggestions.push({
          type: 'fix_original_price',
          title: '修正原价',
          description: `将原价从 ${currentData.originalPrice}元 修正为 ${calculatedOriginalPrice}元`,
          action: {
            field: 'originalPrice',
            value: calculatedOriginalPrice,
            reason: '与订单明细价格保持一致',
          },
          risk: 'low',
          recommended: true,
        });
        break;

      case OrderAmountAnomalyType.CALCULATION_ERROR:
        // 提供多种修复方案
        suggestions.push({
          type: 'fix_total_fee',
          title: '修正实付金额（谨慎操作）',
          description: `将实付金额从 ${currentData.totalFee}元 修正为 ${expectedTotalFee}元`,
          action: {
            field: 'totalFee',
            value: Math.max(0, expectedTotalFee),
            reason: '根据原价和优惠重新计算',
          },
          risk: 'high',
          recommended: false,
          warning: '⚠️ 实付金额与银行流水相关，修改前请确认银行实际扣款金额',
        });

        suggestions.push({
          type: 'fix_original_price',
          title: '修正原价',
          description: `将原价修正为 ${
            currentData.totalFee +
            (currentData.cardDeduction || 0) +
            (currentData.couponDeduction || 0)
          }元`,
          action: {
            field: 'originalPrice',
            value:
              currentData.totalFee +
              (currentData.cardDeduction || 0) +
              (currentData.couponDeduction || 0),
            reason: '根据实付金额和优惠反推原价',
          },
          risk: 'medium',
          recommended: calculatedOriginalPrice === 0, // 如果无法计算明细价格，推荐此方案
        });

        if (
          (currentData.cardDeduction || 0) > 0 ||
          (currentData.couponDeduction || 0) > 0
        ) {
          suggestions.push({
            type: 'fix_discount',
            title: '调整优惠金额',
            description: `调整优惠金额使计算结果与实付金额一致`,
            action: {
              field: 'discount',
              cardDeduction: Math.max(
                0,
                (currentData.originalPrice || calculatedOriginalPrice) -
                  currentData.totalFee -
                  (currentData.couponDeduction || 0)
              ),
              couponDeduction: currentData.couponDeduction || 0,
              reason: '保持原价和实付不变，调整优惠',
            },
            risk: 'medium',
            recommended: false,
          });
        }
        break;

      case OrderAmountAnomalyType.DISCOUNT_ANOMALY:
        suggestions.push({
          type: 'manual_review',
          title: '需要人工审核',
          description: `优惠金额(${
            (currentData.cardDeduction || 0) +
            (currentData.couponDeduction || 0)
          }元)超过原价(${currentData.originalPrice}元)，需要人工分析具体原因`,
          action: null,
          risk: 'high',
          recommended: false,
          warning: '⚠️ 此异常可能涉及业务规则问题，建议人工审核',
        });
        break;
    }

    return {
      orderId: anomaly.orderId,
      orderSn: anomaly.orderSn,
      anomalyType: anomaly.anomalyType,
      description: anomaly.description,
      severity: anomaly.severity,
      currentData,
      calculatedOriginalPrice,
      expectedTotalFee,
      suggestions,
      manualReviewRequired:
        anomaly.anomalyType === OrderAmountAnomalyType.DISCOUNT_ANOMALY,
    };
  }

  /**
   * 应用选定的修复方案
   */
  async applyFixSuggestion(fixData: {
    orderId: number;
    fixType: 'fix_original_price' | 'fix_total_fee' | 'fix_discount';
    value?: number;
    cardDeduction?: number;
    couponDeduction?: number;
    operatorId: number;
    operatorName: string;
    remark?: string;
  }) {
    try {
      const { orderId, fixType, operatorId, operatorName, remark } = fixData;

      // 查找订单
      const order = await Order.findByPk(orderId);
      if (!order) {
        throw new Error('订单不存在');
      }

      const updateData: any = {};
      let fixDescription = '';

      // 根据修复类型执行相应操作
      switch (fixType) {
        case 'fix_original_price':
          if (!fixData.value || fixData.value <= 0) {
            throw new Error('原价值无效');
          }
          updateData.originalPrice = fixData.value;
          fixDescription = `修正原价为: ${fixData.value}元`;
          break;

        case 'fix_total_fee':
          if (fixData.value === undefined || fixData.value < 0) {
            throw new Error('实付金额值无效');
          }
          updateData.totalFee = fixData.value;
          fixDescription = `修正实付金额为: ${fixData.value}元`;
          break;

        case 'fix_discount':
          if (fixData.cardDeduction !== undefined) {
            updateData.cardDeduction = Math.max(0, fixData.cardDeduction);
          }
          if (fixData.couponDeduction !== undefined) {
            updateData.couponDeduction = Math.max(0, fixData.couponDeduction);
          }
          fixDescription = `调整优惠金额: 权益卡${
            updateData.cardDeduction || 0
          }元, 代金券${updateData.couponDeduction || 0}元`;
          break;

        default:
          throw new Error('不支持的修复类型');
      }

      if (Object.keys(updateData).length === 0) {
        throw new Error('无有效的修复数据');
      }

      // 执行更新
      await Order.update(updateData, {
        where: { id: orderId },
      });

      // 记录操作日志
      this.logger.info(`订单${order.sn}金额修复成功`, {
        orderId,
        fixType,
        updateData,
        operatorId,
        operatorName,
        remark,
      });

      return {
        success: true,
        orderId,
        orderSn: order.sn,
        fixType,
        updates: updateData,
        description: fixDescription,
        operator: { id: operatorId, name: operatorName },
        timestamp: new Date(),
      };
    } catch (error) {
      this.logger.error('应用修复方案失败:', error);
      throw error;
    }
  }
}
