import { Provide } from '@midwayjs/core';
import { Vehicle } from '../entity/vehicle.entity';
import { BaseService } from '../common/BaseService';
import { Employee } from '../entity/employee.entity';

@Provide()
export class VehicleService extends BaseService<Vehicle> {
  constructor() {
    super('车辆');
  }

  getModel() {
    return Vehicle;
  }

  async findByPlateNumber(plateNumber: string) {
    return await this.findOne({ where: { plateNumber } });
  }

  async findByVehicleType(vehicleType: string) {
    return await this.findAll({
      query: { vehicleType },
      include: ['employee'],
    });
  }

  async updateLocation(id: number, latitude: number, longitude: number) {
    return await this.update({ id }, { latitude, longitude });
  }

  async updateStatus(id: number, status: string) {
    return await this.update({ id }, { status });
  }

  async getEmployee(vehicleId: number) {
    const vehicle = await Vehicle.findByPk(vehicleId, {
      include: [Employee],
    });
    return vehicle?.employee;
  }

  /**
   * 员工端更新车辆信息
   * @param vehicleId 车辆ID
   * @param employeeId 员工ID
   * @param updateData 更新数据
   */
  async updateVehicleInfo(vehicleId: number, employeeId: number, updateData: any) {
    const now = new Date();
    const updateInfo = {
      ...updateData,
      lastSubmittedAt: now,
      lastSubmittedBy: employeeId,
    };

    return await this.update({ id: vehicleId }, updateInfo);
  }

  /**
   * 获取车辆详细信息（包含提交记录）
   * @param vehicleId 车辆ID
   */
  async getVehicleDetail(vehicleId: number) {
    const vehicle = await Vehicle.findByPk(vehicleId, {
      include: [
        {
          model: Employee,
          as: 'employee',
        },
      ],
    });

    if (!vehicle) {
      return null;
    }

    // 获取最后提交人信息
    let lastSubmittedEmployee = null;
    if (vehicle.lastSubmittedBy) {
      lastSubmittedEmployee = await Employee.findByPk(vehicle.lastSubmittedBy, {
        attributes: ['id', 'name', 'phone'],
      });
    }

    return {
      ...vehicle.toJSON(),
      lastSubmittedEmployee,
    };
  }
}
