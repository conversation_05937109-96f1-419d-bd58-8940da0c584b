// 临时调试脚本
const { createApp } = require('@midwayjs/mock');
const { join } = require('path');

async function debugOrder() {
  try {
    // 创建应用实例
    const app = await createApp(join(__dirname, 'src'), {
      imports: [
        require('@midwayjs/koa'),
        require('@midwayjs/sequelize'),
      ]
    });

    // 获取服务实例
    const orderAmountAnomalyService = await app.getApplicationContext().getAsync('orderAmountAnomalyService');
    
    // 调用调试方法
    console.log('开始调试订单 191...');
    const result = await orderAmountAnomalyService.debugOrderData(191);
    
    console.log('调试结果:', JSON.stringify(result, null, 2));
    
    // 关闭应用
    await app.close();
  } catch (error) {
    console.error('调试失败:', error);
  }
}

debugOrder();
