import {
  Table,
  Column,
  Model,
  DataType,
  BelongsToMany,
  HasMany,
} from 'sequelize-typescript';
import { Service } from './service.entity';
import { ServiceAdditional } from './service-additional.entity';
import { OrderDetail } from './order-detail.entity';
import { OrderDetailAdditional } from './order-detail-additional.entity';
import { ServiceDurationRecord } from './service-duration-record.entity';

export interface AdditionalServiceAttrs {
  /** 增项服务ID */
  id: number;
  /** 服务名称 */
  name: string;
  /** 服务图标 */
  logo?: string;
  /** 服务类型 */
  type: string;
  /** 服务价格 */
  price: number;
  /** 服务时长(分钟) */
  duration?: number;
  /** 是否需要统计时长 */
  needDurationTracking?: boolean;
  /** 服务说明 */
  description?: string;
}

@Table({
  tableName: 'additional_services',
  timestamps: true,
  comment: '增项服务表',
})
export class AdditionalService
  extends Model<AdditionalServiceAttrs>
  implements AdditionalServiceAttrs
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '增项服务ID',
  })
  id: number;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: '服务名称',
  })
  name: string;

  @Column({
    type: DataType.STRING(200),
    comment: '服务图标',
  })
  logo?: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: '服务类型',
  })
  type: string;

  @Column({
    type: DataType.DECIMAL(10, 2),
    allowNull: false,
    comment: '服务价格',
  })
  price: number;

  @Column({
    type: DataType.INTEGER,
    comment: '服务时长(分钟)',
  })
  duration: number;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: true,
    comment: '是否需要统计时长',
  })
  needDurationTracking: boolean;

  @Column({
    type: DataType.STRING(500),
    comment: '服务说明',
  })
  description: string;

  @BelongsToMany(() => Service, () => ServiceAdditional)
  services: Service[];

  @BelongsToMany(() => OrderDetail, () => OrderDetailAdditional)
  orderDetails: OrderDetail[];

  @HasMany(() => ServiceDurationRecord)
  serviceDurationRecords: ServiceDurationRecord[];
}
